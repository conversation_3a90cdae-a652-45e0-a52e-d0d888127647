-- Migration: Create Store Database Tables
-- Database: marcat_stores
-- Created: 2025-07-03

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Stores table
CREATE TABLE stores (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    seller_id UUID NOT NULL, -- References users.id from auth database
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    short_description TEXT,
    logo_url TEXT,
    banner_url TEXT,
    cover_image_url TEXT,
    
    -- Business Information
    business_name VARCHAR(255),
    business_license VARCHAR(100),
    tax_id VARCHAR(50),
    business_type VARCHAR(50) CHECK (business_type IN ('individual', 'company', 'partnership', 'corporation')),
    
    -- Contact Information
    email VARCHAR(255),
    phone VARCHAR(20),
    website_url TEXT,
    
    -- Address Information
    address_line_1 VARCHAR(255),
    address_line_2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(2), -- ISO 3166-1 alpha-2
    
    -- Business Settings
    commission_rate DECIMAL(5,2) DEFAULT 10.00 CHECK (commission_rate >= 0 AND commission_rate <= 100),
    minimum_order_amount DECIMAL(10,2) DEFAULT 0.00,
    free_shipping_threshold DECIMAL(10,2),
    processing_time_days INTEGER DEFAULT 1,
    
    -- Store Status and Metrics
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'suspended', 'closed')),
    verification_status VARCHAR(20) DEFAULT 'unverified' CHECK (verification_status IN ('unverified', 'pending', 'verified', 'rejected')),
    rating DECIMAL(3,2) DEFAULT 0.00 CHECK (rating >= 0 AND rating <= 5),
    total_reviews INTEGER DEFAULT 0,
    total_sales INTEGER DEFAULT 0,
    total_revenue DECIMAL(12,2) DEFAULT 0.00,
    
    -- Store Policies
    return_policy TEXT,
    shipping_policy TEXT,
    privacy_policy TEXT,
    terms_of_service TEXT,
    
    -- Social Media Links
    social_media JSONB DEFAULT '{}', -- {facebook, instagram, twitter, etc.}
    
    -- Store Settings
    settings JSONB DEFAULT '{}', -- Store-specific configuration
    
    -- Timestamps
    approved_at TIMESTAMP,
    last_active_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Store Categories (for store classification)
CREATE TABLE store_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    icon_url TEXT,
    parent_id UUID REFERENCES store_categories(id),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Store Category Assignments
CREATE TABLE store_category_assignments (
    store_id UUID REFERENCES stores(id) ON DELETE CASCADE,
    category_id UUID REFERENCES store_categories(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (store_id, category_id)
);

-- Store Hours
CREATE TABLE store_hours (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    store_id UUID REFERENCES stores(id) ON DELETE CASCADE,
    day_of_week INTEGER CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0 = Sunday
    open_time TIME,
    close_time TIME,
    is_closed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Store Followers (customers following stores)
CREATE TABLE store_followers (
    store_id UUID REFERENCES stores(id) ON DELETE CASCADE,
    user_id UUID NOT NULL, -- References users.id from auth database
    followed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notifications_enabled BOOLEAN DEFAULT TRUE,
    PRIMARY KEY (store_id, user_id)
);

-- Store Announcements
CREATE TABLE store_announcements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    store_id UUID REFERENCES stores(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    type VARCHAR(20) DEFAULT 'general' CHECK (type IN ('general', 'promotion', 'policy', 'maintenance')),
    is_active BOOLEAN DEFAULT TRUE,
    starts_at TIMESTAMP,
    ends_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Store Verification Documents
CREATE TABLE store_verification_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    store_id UUID REFERENCES stores(id) ON DELETE CASCADE,
    document_type VARCHAR(50) NOT NULL CHECK (document_type IN ('business_license', 'tax_certificate', 'identity_proof', 'address_proof', 'bank_statement')),
    document_url TEXT NOT NULL,
    document_name VARCHAR(255),
    file_size INTEGER,
    mime_type VARCHAR(100),
    verification_status VARCHAR(20) DEFAULT 'pending' CHECK (verification_status IN ('pending', 'approved', 'rejected')),
    verified_by UUID, -- References users.id (admin who verified)
    verified_at TIMESTAMP,
    rejection_reason TEXT,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_stores_seller_id ON stores(seller_id);
CREATE INDEX idx_stores_status ON stores(status);
CREATE INDEX idx_stores_verification_status ON stores(verification_status);
CREATE INDEX idx_stores_rating ON stores(rating DESC);
CREATE INDEX idx_stores_created_at ON stores(created_at DESC);
CREATE INDEX idx_stores_slug ON stores(slug);
CREATE INDEX idx_store_categories_parent_id ON store_categories(parent_id);
CREATE INDEX idx_store_categories_slug ON store_categories(slug);
CREATE INDEX idx_store_followers_user_id ON store_followers(user_id);
CREATE INDEX idx_store_announcements_store_id ON store_announcements(store_id);
CREATE INDEX idx_store_announcements_active ON store_announcements(is_active, starts_at, ends_at);

-- Insert default store categories
INSERT INTO store_categories (name, slug, description, sort_order) VALUES
('Fashion & Clothing', 'fashion-clothing', 'Clothing, accessories, and fashion items', 1),
('Formal Wear', 'formal-wear', 'Suits, formal shirts, and business attire', 2),
('Casual Wear', 'casual-wear', 'Casual clothing and everyday wear', 3),
('Sportswear', 'sportswear', 'Athletic and sports clothing', 4),
('Accessories', 'accessories', 'Belts, ties, watches, and other accessories', 5),
('Footwear', 'footwear', 'Shoes, boots, and other footwear', 6);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_stores_updated_at BEFORE UPDATE ON stores
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_store_announcements_updated_at BEFORE UPDATE ON store_announcements
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
