-- Migration: Create Customer Database Tables
-- Database: marcat_customers
-- Created: 2025-07-03

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Shopping Carts
CREATE TABLE carts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID, -- References users.id from auth database (NULL for guest carts)
    session_id VARCHAR(255), -- For guest users
    
    -- Cart Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'abandoned', 'converted', 'expired')),
    
    -- Cart Totals
    subtotal DECIMAL(10,2) DEFAULT 0.00,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    shipping_amount DECIMAL(10,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    
    -- Discount Information
    discount_code VARCHAR(50),
    discount_type VARCHAR(20), -- 'percentage', 'fixed_amount'
    discount_value DECIMAL(10,2),
    
    -- Cart Metadata
    currency VARCHAR(3) DEFAULT 'USD',
    notes TEXT,
    
    -- Timestamps
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Cart Items
CREATE TABLE cart_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cart_id UUID REFERENCES carts(id) ON DELETE CASCADE,
    product_id UUID NOT NULL, -- References products.id from product database
    variant_id UUID NOT NULL, -- References product_variants.id from product database
    store_id UUID NOT NULL, -- References stores.id from store database
    
    -- Item Details
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    
    -- Product Snapshot (at time of adding to cart)
    product_name VARCHAR(255) NOT NULL,
    variant_sku VARCHAR(100),
    variant_attributes JSONB, -- {color, size, etc.}
    product_image_url TEXT,
    
    -- Item Status
    is_available BOOLEAN DEFAULT TRUE,
    availability_message TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Wishlists
CREATE TABLE wishlists (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id from auth database
    
    -- Wishlist Details
    name VARCHAR(255) DEFAULT 'My Wishlist',
    description TEXT,
    
    -- Wishlist Settings
    is_public BOOLEAN DEFAULT FALSE,
    is_default BOOLEAN DEFAULT FALSE,
    
    -- Wishlist Metadata
    total_items INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Wishlist Items
CREATE TABLE wishlist_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wishlist_id UUID REFERENCES wishlists(id) ON DELETE CASCADE,
    product_id UUID NOT NULL, -- References products.id from product database
    variant_id UUID, -- References product_variants.id (optional, for specific variant)
    store_id UUID NOT NULL, -- References stores.id from store database
    
    -- Item Details
    notes TEXT,
    priority INTEGER DEFAULT 0, -- 0 = normal, 1 = high, -1 = low
    
    -- Product Snapshot
    product_name VARCHAR(255) NOT NULL,
    product_price DECIMAL(10,2),
    product_image_url TEXT,
    variant_attributes JSONB,
    
    -- Item Status
    is_available BOOLEAN DEFAULT TRUE,
    price_alert_enabled BOOLEAN DEFAULT FALSE,
    target_price DECIMAL(10,2),
    
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Customer Preferences
CREATE TABLE customer_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID UNIQUE NOT NULL, -- References users.id from auth database
    
    -- Communication Preferences
    email_notifications BOOLEAN DEFAULT TRUE,
    sms_notifications BOOLEAN DEFAULT FALSE,
    push_notifications BOOLEAN DEFAULT TRUE,
    
    -- Notification Types
    order_updates BOOLEAN DEFAULT TRUE,
    promotional_emails BOOLEAN DEFAULT TRUE,
    price_alerts BOOLEAN DEFAULT TRUE,
    restock_alerts BOOLEAN DEFAULT TRUE,
    newsletter BOOLEAN DEFAULT FALSE,
    
    -- Shopping Preferences
    preferred_currency VARCHAR(3) DEFAULT 'USD',
    preferred_language VARCHAR(5) DEFAULT 'en',
    size_preferences JSONB, -- {shirt: 'L', pants: '32', shoes: '10'}
    
    -- Privacy Settings
    profile_visibility VARCHAR(20) DEFAULT 'private' CHECK (profile_visibility IN ('public', 'friends', 'private')),
    show_purchase_history BOOLEAN DEFAULT FALSE,
    allow_recommendations BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Customer Addresses (extended from auth database)
CREATE TABLE customer_addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id from auth database
    
    -- Address Type
    type VARCHAR(20) DEFAULT 'shipping' CHECK (type IN ('shipping', 'billing', 'both')),
    label VARCHAR(50), -- 'Home', 'Work', 'Other'
    
    -- Address Details
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    company VARCHAR(100),
    address_line_1 VARCHAR(255) NOT NULL,
    address_line_2 VARCHAR(255),
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100),
    postal_code VARCHAR(20) NOT NULL,
    country VARCHAR(2) NOT NULL, -- ISO 3166-1 alpha-2
    phone VARCHAR(20),
    
    -- Address Settings
    is_default BOOLEAN DEFAULT FALSE,
    is_verified BOOLEAN DEFAULT FALSE,
    
    -- Delivery Instructions
    delivery_instructions TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Recently Viewed Products
CREATE TABLE recently_viewed (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID, -- References users.id (NULL for guest users)
    session_id VARCHAR(255), -- For guest users
    product_id UUID NOT NULL, -- References products.id
    store_id UUID NOT NULL, -- References stores.id
    
    -- View Details
    view_count INTEGER DEFAULT 1,
    last_viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Product Snapshot
    product_name VARCHAR(255),
    product_price DECIMAL(10,2),
    product_image_url TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(COALESCE(user_id::text, session_id), product_id)
);

-- Customer Search History
CREATE TABLE search_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID, -- References users.id (NULL for guest users)
    session_id VARCHAR(255), -- For guest users
    
    -- Search Details
    search_query VARCHAR(500) NOT NULL,
    search_filters JSONB, -- Applied filters
    results_count INTEGER DEFAULT 0,
    
    -- Search Context
    search_source VARCHAR(50) DEFAULT 'web', -- 'web', 'mobile', 'api'
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Customer Segments (for marketing and analytics)
CREATE TABLE customer_segments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- Segment Criteria
    criteria JSONB NOT NULL, -- Conditions for segment membership
    
    -- Segment Settings
    is_active BOOLEAN DEFAULT TRUE,
    auto_update BOOLEAN DEFAULT TRUE,
    
    -- Segment Metrics
    customer_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Customer Segment Memberships
CREATE TABLE customer_segment_memberships (
    user_id UUID NOT NULL, -- References users.id
    segment_id UUID REFERENCES customer_segments(id) ON DELETE CASCADE,
    
    -- Membership Details
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    
    PRIMARY KEY (user_id, segment_id)
);

-- Indexes for performance
CREATE INDEX idx_carts_user_id ON carts(user_id);
CREATE INDEX idx_carts_session_id ON carts(session_id);
CREATE INDEX idx_carts_status ON carts(status);
CREATE INDEX idx_carts_last_activity ON carts(last_activity_at DESC);

CREATE INDEX idx_cart_items_cart_id ON cart_items(cart_id);
CREATE INDEX idx_cart_items_product_id ON cart_items(product_id);
CREATE INDEX idx_cart_items_store_id ON cart_items(store_id);

CREATE INDEX idx_wishlists_user_id ON wishlists(user_id);
CREATE INDEX idx_wishlists_is_public ON wishlists(is_public);

CREATE INDEX idx_wishlist_items_wishlist_id ON wishlist_items(wishlist_id);
CREATE INDEX idx_wishlist_items_product_id ON wishlist_items(product_id);
CREATE INDEX idx_wishlist_items_store_id ON wishlist_items(store_id);

CREATE INDEX idx_customer_preferences_user_id ON customer_preferences(user_id);

CREATE INDEX idx_customer_addresses_user_id ON customer_addresses(user_id);
CREATE INDEX idx_customer_addresses_type ON customer_addresses(type);
CREATE INDEX idx_customer_addresses_is_default ON customer_addresses(is_default);

CREATE INDEX idx_recently_viewed_user_id ON recently_viewed(user_id);
CREATE INDEX idx_recently_viewed_session_id ON recently_viewed(session_id);
CREATE INDEX idx_recently_viewed_product_id ON recently_viewed(product_id);
CREATE INDEX idx_recently_viewed_last_viewed ON recently_viewed(last_viewed_at DESC);

CREATE INDEX idx_search_history_user_id ON search_history(user_id);
CREATE INDEX idx_search_history_session_id ON search_history(session_id);
CREATE INDEX idx_search_history_query ON search_history(search_query);
CREATE INDEX idx_search_history_created_at ON search_history(created_at DESC);

CREATE INDEX idx_customer_segment_memberships_user_id ON customer_segment_memberships(user_id);
CREATE INDEX idx_customer_segment_memberships_segment_id ON customer_segment_memberships(segment_id);

-- Function to update cart totals
CREATE OR REPLACE FUNCTION update_cart_totals(p_cart_id UUID)
RETURNS VOID AS $$
DECLARE
    cart_subtotal DECIMAL(10,2);
BEGIN
    -- Calculate subtotal from cart items
    SELECT COALESCE(SUM(total_price), 0.00)
    INTO cart_subtotal
    FROM cart_items
    WHERE cart_id = p_cart_id;
    
    -- Update cart totals (simplified - tax and shipping would be calculated separately)
    UPDATE carts
    SET subtotal = cart_subtotal,
        total_amount = cart_subtotal - COALESCE(discount_amount, 0.00),
        updated_at = CURRENT_TIMESTAMP,
        last_activity_at = CURRENT_TIMESTAMP
    WHERE id = p_cart_id;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up expired carts
CREATE OR REPLACE FUNCTION cleanup_expired_carts()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete carts that have been inactive for more than 30 days
    DELETE FROM carts
    WHERE status = 'active'
      AND last_activity_at < CURRENT_TIMESTAMP - INTERVAL '30 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_carts_updated_at BEFORE UPDATE ON carts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cart_items_updated_at BEFORE UPDATE ON cart_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_wishlists_updated_at BEFORE UPDATE ON wishlists
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_preferences_updated_at BEFORE UPDATE ON customer_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_addresses_updated_at BEFORE UPDATE ON customer_addresses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_segments_updated_at BEFORE UPDATE ON customer_segments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger to update cart totals when items change
CREATE OR REPLACE FUNCTION trigger_update_cart_totals()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        PERFORM update_cart_totals(OLD.cart_id);
        RETURN OLD;
    ELSE
        PERFORM update_cart_totals(NEW.cart_id);
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_cart_totals_on_item_change
    AFTER INSERT OR UPDATE OR DELETE ON cart_items
    FOR EACH ROW EXECUTE FUNCTION trigger_update_cart_totals();
