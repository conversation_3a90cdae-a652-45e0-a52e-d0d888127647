version: '3.8'

services:
  # Databases
  postgres-auth:
    image: postgres:15-alpine
    container_name: marcat-auth-db
    environment:
      POSTGRES_DB: marcat_auth
      POSTGRES_USER: marcat_user
      POSTGRES_PASSWORD: marcat_password
    ports:
      - "5432:5432"
    volumes:
      - auth_db_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - marcat-network

  postgres-products:
    image: postgres:15-alpine
    container_name: marcat-products-db
    environment:
      POSTGRES_DB: marcat_products
      POSTGRES_USER: marcat_user
      POSTGRES_PASSWORD: marcat_password
    ports:
      - "5433:5432"
    volumes:
      - products_db_data:/var/lib/postgresql/data
    networks:
      - marcat-network

  postgres-stores:
    image: postgres:15-alpine
    container_name: marcat-stores-db
    environment:
      POSTGRES_DB: marcat_stores
      POSTGRES_USER: marcat_user
      POSTGRES_PASSWORD: marcat_password
    ports:
      - "5434:5432"
    volumes:
      - stores_db_data:/var/lib/postgresql/data
    networks:
      - marcat-network

  postgres-orders:
    image: postgres:15-alpine
    container_name: marcat-orders-db
    environment:
      POSTGRES_DB: marcat_orders
      POSTGRES_USER: marcat_user
      POSTGRES_PASSWORD: marcat_password
    ports:
      - "5435:5432"
    volumes:
      - orders_db_data:/var/lib/postgresql/data
    networks:
      - marcat-network

  postgres-payments:
    image: postgres:15-alpine
    container_name: marcat-payments-db
    environment:
      POSTGRES_DB: marcat_payments
      POSTGRES_USER: marcat_user
      POSTGRES_PASSWORD: marcat_password
    ports:
      - "5436:5432"
    volumes:
      - payments_db_data:/var/lib/postgresql/data
    networks:
      - marcat-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: marcat-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - marcat-network

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: marcat-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: marcat
      RABBITMQ_DEFAULT_PASS: marcat_queue
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - marcat-network

  # API Gateway
  api-gateway:
    build:
      context: ../../backend/api-gateway
      dockerfile: Dockerfile
    container_name: marcat-api-gateway
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
    depends_on:
      - redis
      - rabbitmq
    networks:
      - marcat-network
    volumes:
      - ../../backend/api-gateway:/app
      - /app/node_modules

  # Auth Service
  auth-service:
    build:
      context: ../../backend/services/auth-service
      dockerfile: Dockerfile
    container_name: marcat-auth-service
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - AUTH_DB_HOST=postgres-auth
      - AUTH_DB_PORT=5432
      - AUTH_DB_NAME=marcat_auth
      - AUTH_DB_USER=marcat_user
      - AUTH_DB_PASSWORD=marcat_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - postgres-auth
      - redis
    networks:
      - marcat-network
    volumes:
      - ../../backend/services/auth-service:/app
      - /app/node_modules

  # Product Service
  product-service:
    build:
      context: ../../backend/services/product-service
      dockerfile: Dockerfile
    container_name: marcat-product-service
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - PRODUCT_DB_HOST=postgres-products
      - PRODUCT_DB_PORT=5432
      - PRODUCT_DB_NAME=marcat_products
      - PRODUCT_DB_USER=marcat_user
      - PRODUCT_DB_PASSWORD=marcat_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - postgres-products
      - redis
    networks:
      - marcat-network
    volumes:
      - ../../backend/services/product-service:/app
      - /app/node_modules

  # Store Service
  store-service:
    build:
      context: ../../backend/services/store-service
      dockerfile: Dockerfile
    container_name: marcat-store-service
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - STORE_DB_HOST=postgres-stores
      - STORE_DB_PORT=5432
      - STORE_DB_NAME=marcat_stores
      - STORE_DB_USER=marcat_user
      - STORE_DB_PASSWORD=marcat_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - postgres-stores
      - redis
    networks:
      - marcat-network
    volumes:
      - ../../backend/services/store-service:/app
      - /app/node_modules

  # Order Service
  order-service:
    build:
      context: ../../backend/services/order-service
      dockerfile: Dockerfile
    container_name: marcat-order-service
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - PORT=3004
      - ORDER_DB_HOST=postgres-orders
      - ORDER_DB_PORT=5432
      - ORDER_DB_NAME=marcat_orders
      - ORDER_DB_USER=marcat_user
      - ORDER_DB_PASSWORD=marcat_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
    depends_on:
      - postgres-orders
      - redis
      - rabbitmq
    networks:
      - marcat-network
    volumes:
      - ../../backend/services/order-service:/app
      - /app/node_modules

  # Payment Service
  payment-service:
    build:
      context: ../../backend/services/payment-service
      dockerfile: Dockerfile
    container_name: marcat-payment-service
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=development
      - PORT=3005
      - PAYMENT_DB_HOST=postgres-payments
      - PAYMENT_DB_PORT=5432
      - PAYMENT_DB_NAME=marcat_payments
      - PAYMENT_DB_USER=marcat_user
      - PAYMENT_DB_PASSWORD=marcat_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
    depends_on:
      - postgres-payments
      - redis
      - rabbitmq
    networks:
      - marcat-network
    volumes:
      - ../../backend/services/payment-service:/app
      - /app/node_modules

volumes:
  auth_db_data:
  products_db_data:
  stores_db_data:
  orders_db_data:
  payments_db_data:
  redis_data:
  rabbitmq_data:

networks:
  marcat-network:
    driver: bridge
