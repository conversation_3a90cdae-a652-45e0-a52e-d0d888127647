# Database Schema Design

## Overview

The Marcat e-commerce platform uses a microservices architecture with separate databases for each service. This approach provides better scalability, maintainability, and allows teams to work independently on different services.

## Database Distribution

### Core Databases

1. **marcat_auth** - User authentication and authorization
2. **marcat_stores** - Store management and seller information
3. **marcat_products** - Product catalog and variants
4. **marcat_orders** - Order processing and fulfillment
5. **marcat_payments** - Payment processing and transactions
6. **marcat_inventory** - Stock management and tracking
7. **marcat_customers** - Customer data and preferences
8. **marcat_reviews** - Product and store reviews
9. **marcat_loyalty** - Loyalty program and rewards
10. **marcat_notifications** - Notification system
11. **marcat_support** - Customer support and help desk

## Key Design Principles

- **UUID Primary Keys**: All tables use UUID primary keys for better distribution and security
- **Soft Deletes**: Important records use status fields instead of hard deletes
- **Audit Trails**: Created/updated timestamps on all major entities
- **Referential Integrity**: Foreign key relationships maintained within databases
- **Cross-Service References**: UUID references to entities in other databases
- **Indexing Strategy**: Comprehensive indexing for query performance
- **Data Consistency**: Triggers and functions maintain data integrity
- **Business Logic**: Database functions handle complex business rules
- **Performance Optimization**: Views and materialized views for complex queries

## Database Schemas

### 1. Authentication Database (marcat_auth)

**Core Tables:**
- `users` - User accounts and basic information
- `roles` - System roles (admin, customer, store_owner, support_agent)
- `user_roles` - User role assignments
- `user_sessions` - Active user sessions
- `addresses` - User addresses for shipping/billing

**Key Features:**
- Role-based access control (RBAC)
- Session management with expiration
- Password reset functionality
- Email verification system
- Address management with default settings

### 2. Store Database (marcat_stores)

**Core Tables:**
- `stores` - Store information and settings
- `store_categories` - Store categorization
- `store_category_assignments` - Store-category relationships
- `store_followers` - Customer store following
- `store_analytics` - Store performance metrics

**Key Features:**
- Multi-store support with commission tracking
- Store verification system
- Category-based store organization
- Store following/subscription system
- Performance analytics and reporting

### 3. Product Database (marcat_products)

**Core Tables:**
- `categories` - Product categories with hierarchy
- `products` - Product information and details
- `product_variants` - Product variations (size, color, etc.)
- `product_images` - Product and variant images
- `collections` - Curated product collections
- `product_attributes` - Flexible attribute system

**Key Features:**
- Hierarchical category system
- Flexible product variant system
- Multi-image support per variant
- SEO-optimized product URLs
- Advanced search capabilities
- Collection and curation system

### 4. Order Database (marcat_orders)

**Core Tables:**
- `orders` - Order header information
- `order_items` - Individual order line items
- `order_status_history` - Order status tracking
- `shipments` - Shipping information
- `return_requests` - Return and refund requests
- `commissions` - Seller commission tracking

**Key Features:**
- Multi-store order support
- Comprehensive order tracking
- Commission calculation and tracking
- Return and refund management
- Shipping integration
- Order analytics

### 5. Payment Database (marcat_payments)

**Core Tables:**
- `payment_methods` - Stored payment methods
- `payment_transactions` - Payment processing records
- `refunds` - Refund transactions
- `payment_intents` - Payment intent tracking
- `payment_webhooks` - Webhook event processing
- `payment_disputes` - Chargeback and dispute handling

**Key Features:**
- Multiple payment gateway support
- Secure payment method storage
- Comprehensive transaction tracking
- Refund and dispute management
- Webhook processing
- Payment analytics

### 6. Inventory Database (marcat_inventory)

**Core Tables:**
- `inventory` - Stock levels and management
- `stock_movements` - Inventory transaction history
- `low_stock_alerts` - Automated stock alerts
- `inventory_adjustments` - Manual stock adjustments
- `inventory_transfers` - Inter-location transfers
- `inventory_reservations` - Stock reservations

**Key Features:**
- Real-time inventory tracking
- Multi-location inventory support
- Automated reorder point alerts
- Stock movement history
- Reservation system for pending orders
- Inventory transfer management

### 7. Customer Database (marcat_customers)

**Core Tables:**
- `carts` - Shopping cart management
- `cart_items` - Cart line items
- `wishlists` - Customer wishlists
- `wishlist_items` - Wishlist products
- `customer_preferences` - User preferences
- `recently_viewed` - Browsing history
- `search_history` - Search behavior tracking

**Key Features:**
- Persistent shopping carts
- Multiple wishlist support
- Customer preference management
- Browsing history tracking
- Search behavior analytics
- Customer segmentation

### 8. Review Database (marcat_reviews)

**Core Tables:**
- `reviews` - Product and store reviews
- `review_images` - Review photos
- `review_votes` - Review helpfulness voting
- `review_responses` - Store owner responses
- `review_reports` - Content moderation
- `review_analytics` - Review metrics

**Key Features:**
- Multi-type reviews (product, store, order)
- Image support in reviews
- Review moderation system
- Store owner response capability
- Review helpfulness voting
- Comprehensive review analytics

### 9. Loyalty Database (marcat_loyalty)

**Core Tables:**
- `loyalty_tiers` - Loyalty program tiers
- `loyalty_points` - User points balance
- `points_transactions` - Points earning/spending history
- `rewards` - Available rewards catalog
- `reward_redemptions` - Reward usage tracking
- `referrals` - Referral program management

**Key Features:**
- Tiered loyalty program
- Points earning and redemption
- Comprehensive rewards catalog
- Referral program
- Loyalty campaigns
- Performance analytics

### 10. Notification Database (marcat_notifications)

**Core Tables:**
- `notification_templates` - Message templates
- `notifications` - Notification records
- `email_notifications` - Email delivery tracking
- `sms_notifications` - SMS delivery tracking
- `push_notifications` - Push notification tracking
- `user_notification_preferences` - User preferences

**Key Features:**
- Multi-channel notifications (email, SMS, push, in-app)
- Template-based messaging
- Delivery tracking and analytics
- User preference management
- Campaign management
- Notification scheduling

### 11. Support Database (marcat_support)

**Core Tables:**
- `support_categories` - Ticket categorization
- `support_tickets` - Support ticket management
- `ticket_messages` - Ticket conversation history
- `kb_articles` - Knowledge base articles
- `faqs` - Frequently asked questions
- `chat_sessions` - Live chat support

**Key Features:**
- Comprehensive ticket management
- Knowledge base system
- FAQ management
- Live chat support
- Agent performance tracking
- Customer satisfaction measurement

## Cross-Database Relationships

Since each microservice has its own database, relationships between services are maintained through UUID references:

- **User References**: All services reference `users.id` from the auth database
- **Store References**: Product, order, and inventory services reference `stores.id`
- **Product References**: Order, inventory, and review services reference `products.id`
- **Order References**: Payment and support services reference `orders.id`

## Data Consistency Strategy

1. **Event-Driven Updates**: Services publish events when data changes
2. **Eventual Consistency**: Cross-service data synchronization through events
3. **Compensating Transactions**: Rollback mechanisms for failed operations
4. **Data Validation**: Business rules enforced at service boundaries
5. **Monitoring**: Health checks and data consistency monitoring

## Performance Considerations

1. **Indexing**: Comprehensive indexing strategy for all query patterns
2. **Partitioning**: Large tables partitioned by date or other criteria
3. **Caching**: Redis caching layer for frequently accessed data
4. **Read Replicas**: Read-only replicas for reporting and analytics
5. **Connection Pooling**: Efficient database connection management

## Security Measures

1. **Encryption**: All sensitive data encrypted at rest and in transit
2. **Access Control**: Database-level access controls and permissions
3. **Audit Logging**: Comprehensive audit trails for all data changes
4. **Data Masking**: Sensitive data masked in non-production environments
5. **Backup Strategy**: Regular backups with point-in-time recovery

## Migration and Deployment

### Database Setup Process

1. **Create Databases**: Run `database/setup_databases.sql` to create all databases
2. **Run Migrations**: Execute migration files in order (001-011)
3. **Seed Data**: Optionally run `database/seeds/001_seed_sample_data.sql` for development
4. **Verify Setup**: Run health checks to ensure all databases are properly configured

### Migration Files

- `001_create_auth_tables.sql` - Authentication and user management
- `002_create_store_tables.sql` - Store and seller management
- `003_create_product_tables.sql` - Product catalog and variants
- `004_create_order_tables.sql` - Order processing and commissions
- `005_create_payment_tables.sql` - Payment processing and transactions
- `006_create_inventory_tables.sql` - Inventory management and tracking
- `007_create_customer_tables.sql` - Customer data and preferences
- `008_create_review_tables.sql` - Review and rating system
- `009_create_loyalty_tables.sql` - Loyalty program and rewards
- `010_create_notification_tables.sql` - Notification system
- `011_create_support_tables.sql` - Customer support system

### Development Setup

```bash
# Create and setup all databases
psql -U postgres -f database/setup_databases.sql

# Populate with sample data (development only)
psql -U postgres -f database/seeds/001_seed_sample_data.sql
```

### Production Considerations

- Use environment-specific configuration files
- Implement proper backup and recovery procedures
- Set up monitoring and alerting for database health
- Configure read replicas for reporting workloads
- Implement proper access controls and security measures

### Payment Database
- Payment_Transactions
- Payment_Methods
- Refunds

### Inventory Database
- Inventory
- Stock_Movements
- Low_Stock_Alerts

### Customer Database
- Carts
- Cart_Items
- Wishlists
- Wishlist_Items

### Review Database
- Reviews
- Review_Images

### Loyalty Database
- Loyalty_Points
- Rewards
- User_Rewards

### Notification Database
- Notifications
- Notification_Templates

### Support Database
- Support_Tickets
- Chat_Messages

## Core Entity Relationships

### User Management
```sql
-- Users table (Auth Database)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    date_of_birth DATE,
    gender VARCHAR(10),
    profile_image_url TEXT,
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Roles table
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User_Roles junction table
CREATE TABLE user_roles (
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id)
);
```

### Store & Product Management
```sql
-- Stores table (Store Database)
CREATE TABLE stores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    seller_id UUID NOT NULL, -- References users.id
    name VARCHAR(255) NOT NULL,
    description TEXT,
    logo_url TEXT,
    banner_url TEXT,
    address JSONB, -- {street, city, state, country, postal_code}
    contact_info JSONB, -- {phone, email, website}
    business_license VARCHAR(100),
    tax_id VARCHAR(50),
    commission_rate DECIMAL(5,2) DEFAULT 10.00,
    status VARCHAR(20) DEFAULT 'active',
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Categories table (Product Database)
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES categories(id),
    image_url TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Products table (Product Database)
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    store_id UUID NOT NULL, -- References stores.id
    category_id UUID REFERENCES categories(id),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT,
    short_description TEXT,
    brand VARCHAR(100),
    material VARCHAR(100),
    care_instructions TEXT,
    base_price DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    is_featured BOOLEAN DEFAULT FALSE,
    weight DECIMAL(8,2),
    dimensions JSONB, -- {length, width, height}
    tags TEXT[],
    seo_title VARCHAR(255),
    seo_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Product_Variants table (Product Database)
CREATE TABLE product_variants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    sku VARCHAR(100) UNIQUE NOT NULL,
    color VARCHAR(50),
    size VARCHAR(20),
    price DECIMAL(10,2) NOT NULL,
    compare_at_price DECIMAL(10,2),
    cost_price DECIMAL(10,2),
    weight DECIMAL(8,2),
    barcode VARCHAR(100),
    image_urls TEXT[],
    is_default BOOLEAN DEFAULT FALSE,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Order & Commission Management
```sql
-- Orders table (Order Database)
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL, -- References users.id
    order_number VARCHAR(50) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    shipping_amount DECIMAL(10,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    shipping_address JSONB,
    billing_address JSONB,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Order_Items table (Order Database)
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID NOT NULL, -- References products.id
    variant_id UUID NOT NULL, -- References product_variants.id
    store_id UUID NOT NULL, -- References stores.id
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    commission_rate DECIMAL(5,2) NOT NULL,
    commission_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Commissions table (Order Database)
CREATE TABLE commissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    seller_id UUID NOT NULL, -- References users.id
    store_id UUID NOT NULL, -- References stores.id
    order_id UUID REFERENCES orders(id),
    order_item_id UUID REFERENCES order_items(id),
    amount DECIMAL(10,2) NOT NULL,
    rate DECIMAL(5,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    paid_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Payment & Transaction Management
```sql
-- Payment_Transactions table (Payment Database)
CREATE TABLE payment_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL, -- References orders.id
    transaction_id VARCHAR(255) UNIQUE NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    gateway VARCHAR(50) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(20) DEFAULT 'pending',
    gateway_response JSONB,
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Refunds table (Payment Database)
CREATE TABLE refunds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID REFERENCES payment_transactions(id),
    order_id UUID NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    reason TEXT,
    status VARCHAR(20) DEFAULT 'pending',
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Inventory Management
```sql
-- Inventory table (Inventory Database)
CREATE TABLE inventory (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    variant_id UUID UNIQUE NOT NULL, -- References product_variants.id
    store_id UUID NOT NULL, -- References stores.id
    quantity_available INTEGER NOT NULL DEFAULT 0,
    quantity_reserved INTEGER NOT NULL DEFAULT 0,
    quantity_sold INTEGER NOT NULL DEFAULT 0,
    reorder_point INTEGER DEFAULT 10,
    max_stock_level INTEGER,
    last_restocked_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Stock_Movements table (Inventory Database)
CREATE TABLE stock_movements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    variant_id UUID NOT NULL, -- References product_variants.id
    movement_type VARCHAR(20) NOT NULL, -- 'in', 'out', 'adjustment'
    quantity INTEGER NOT NULL,
    reference_type VARCHAR(50), -- 'order', 'restock', 'adjustment'
    reference_id UUID,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Indexes and Performance

### Primary Indexes
- All primary keys are UUIDs with B-tree indexes
- Foreign key relationships have automatic indexes

### Secondary Indexes
```sql
-- Product search optimization
CREATE INDEX idx_products_category_status ON products(category_id, status);
CREATE INDEX idx_products_store_status ON products(store_id, status);
CREATE INDEX idx_products_name_search ON products USING gin(to_tsvector('english', name));

-- Order performance
CREATE INDEX idx_orders_customer_status ON orders(customer_id, status);
CREATE INDEX idx_orders_created_at ON orders(created_at DESC);

-- Inventory tracking
CREATE INDEX idx_inventory_variant ON inventory(variant_id);
CREATE INDEX idx_inventory_low_stock ON inventory(quantity_available) WHERE quantity_available <= reorder_point;
```

## Data Relationships Summary

1. **Users** have multiple **Roles** (many-to-many)
2. **Users** can own multiple **Stores** (one-to-many)
3. **Stores** have multiple **Products** (one-to-many)
4. **Products** belong to **Categories** (many-to-one)
5. **Products** have multiple **Variants** (one-to-many)
6. **Orders** contain multiple **Order Items** (one-to-many)
7. **Order Items** reference **Product Variants** (many-to-one)
8. **Inventory** tracks **Product Variants** (one-to-one)
9. **Reviews** are for **Products** by **Users** (many-to-many)
10. **Commissions** are calculated from **Order Items** (one-to-many)
