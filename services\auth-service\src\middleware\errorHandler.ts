import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '@/types';
import { logger } from '@/utils/logger';
import { config } from '@/config';

/**
 * Custom error class for API errors
 */
export class ApiError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Error handler middleware
 */
export const errorHandler = (
  error: Error | ApiError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let statusCode = 500;
  let message = 'Internal server error';
  let errors: string[] = [];

  // Handle different types of errors
  if (error instanceof ApiError) {
    statusCode = error.statusCode;
    message = error.message;
  } else if (error.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation error';
    errors = Object.values(error).map((err: any) => err.message);
  } else if (error.name === 'CastError') {
    statusCode = 400;
    message = 'Invalid ID format';
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Invalid token';
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expired';
  } else if (error.message.includes('duplicate key')) {
    statusCode = 409;
    message = 'Resource already exists';
  } else if (error.message.includes('foreign key constraint')) {
    statusCode = 400;
    message = 'Invalid reference to related resource';
  } else if (error.message.includes('not found')) {
    statusCode = 404;
    message = 'Resource not found';
  }

  // Log error details
  logger.error('Error occurred', {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    request: {
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      userId: (req as any).user?.id,
    },
    statusCode,
  });

  // Prepare response
  const response: ApiResponse = {
    success: false,
    message,
    ...(errors.length > 0 && { errors }),
  };

  // Include stack trace in development
  if (config.server.nodeEnv === 'development') {
    response.data = {
      stack: error.stack,
      name: error.name,
    };
  }

  res.status(statusCode).json(response);
};

/**
 * Async error wrapper
 * Wraps async route handlers to catch errors and pass them to error handler
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Not found error
 */
export const notFound = (req: Request, res: Response, next: NextFunction): void => {
  const error = new ApiError(`Not found - ${req.originalUrl}`, 404);
  next(error);
};

/**
 * Database connection error handler
 */
export const handleDatabaseError = (error: any): ApiError => {
  if (error.code === 'ECONNREFUSED') {
    return new ApiError('Database connection failed', 503);
  } else if (error.code === '23505') { // PostgreSQL unique violation
    return new ApiError('Resource already exists', 409);
  } else if (error.code === '23503') { // PostgreSQL foreign key violation
    return new ApiError('Invalid reference to related resource', 400);
  } else if (error.code === '23502') { // PostgreSQL not null violation
    return new ApiError('Required field is missing', 400);
  } else if (error.code === '22001') { // PostgreSQL string data right truncation
    return new ApiError('Data too long for field', 400);
  } else if (error.code === '42P01') { // PostgreSQL undefined table
    return new ApiError('Database table not found', 500);
  } else if (error.code === '42703') { // PostgreSQL undefined column
    return new ApiError('Database column not found', 500);
  }

  return new ApiError('Database operation failed', 500);
};

/**
 * Redis connection error handler
 */
export const handleRedisError = (error: any): ApiError => {
  if (error.code === 'ECONNREFUSED') {
    return new ApiError('Cache service unavailable', 503);
  } else if (error.code === 'NOAUTH') {
    return new ApiError('Cache authentication failed', 500);
  }

  return new ApiError('Cache operation failed', 500);
};

/**
 * File upload error handler
 */
export const handleUploadError = (error: any): ApiError => {
  if (error.code === 'LIMIT_FILE_SIZE') {
    return new ApiError('File too large', 413);
  } else if (error.code === 'LIMIT_FILE_COUNT') {
    return new ApiError('Too many files', 400);
  } else if (error.code === 'LIMIT_UNEXPECTED_FILE') {
    return new ApiError('Unexpected file field', 400);
  }

  return new ApiError('File upload failed', 500);
};

/**
 * Email service error handler
 */
export const handleEmailError = (error: any): ApiError => {
  if (error.code === 'EAUTH') {
    return new ApiError('Email authentication failed', 500);
  } else if (error.code === 'ECONNECTION') {
    return new ApiError('Email service unavailable', 503);
  }

  return new ApiError('Email sending failed', 500);
};

/**
 * Create API error
 */
export const createApiError = (message: string, statusCode: number = 500): ApiError => {
  return new ApiError(message, statusCode);
};

/**
 * Bad request error
 */
export const badRequest = (message: string = 'Bad request'): ApiError => {
  return new ApiError(message, 400);
};

/**
 * Unauthorized error
 */
export const unauthorized = (message: string = 'Unauthorized'): ApiError => {
  return new ApiError(message, 401);
};

/**
 * Forbidden error
 */
export const forbidden = (message: string = 'Forbidden'): ApiError => {
  return new ApiError(message, 403);
};

/**
 * Not found error
 */
export const notFoundError = (message: string = 'Not found'): ApiError => {
  return new ApiError(message, 404);
};

/**
 * Conflict error
 */
export const conflict = (message: string = 'Conflict'): ApiError => {
  return new ApiError(message, 409);
};

/**
 * Internal server error
 */
export const internalServerError = (message: string = 'Internal server error'): ApiError => {
  return new ApiError(message, 500);
};

/**
 * Service unavailable error
 */
export const serviceUnavailable = (message: string = 'Service unavailable'): ApiError => {
  return new ApiError(message, 503);
};

export default errorHandler;
