# Marcat Platform Environment Configuration

# Application Environment
NODE_ENV=development
PORT=3000

# API Gateway Configuration
API_GATEWAY_PORT=3000
API_GATEWAY_HOST=localhost

# Service Ports
AUTH_SERVICE_PORT=3001
PRODUCT_SERVICE_PORT=3002
STORE_SERVICE_PORT=3003
ORDER_SERVICE_PORT=3004
PAYMENT_SERVICE_PORT=3005
INVENTORY_SERVICE_PORT=3006
NOTIFICATION_SERVICE_PORT=3007
REVIEW_SERVICE_PORT=3008
LOYALTY_SERVICE_PORT=3009
RECOMMENDATION_SERVICE_PORT=3010
SUPPORT_SERVICE_PORT=3011

# Database Configuration
# Auth Database
AUTH_DB_HOST=localhost
AUTH_DB_PORT=5432
AUTH_DB_NAME=marcat_auth
AUTH_DB_USER=marcat_user
AUTH_DB_PASSWORD=marcat_password

# Product Database
PRODUCT_DB_HOST=localhost
PRODUCT_DB_PORT=5432
PRODUCT_DB_NAME=marcat_products
PRODUCT_DB_USER=marcat_user
PRODUCT_DB_PASSWORD=marcat_password

# Store Database
STORE_DB_HOST=localhost
STORE_DB_PORT=5432
STORE_DB_NAME=marcat_stores
STORE_DB_USER=marcat_user
STORE_DB_PASSWORD=marcat_password

# Order Database
ORDER_DB_HOST=localhost
ORDER_DB_PORT=5432
ORDER_DB_NAME=marcat_orders
ORDER_DB_USER=marcat_user
ORDER_DB_PASSWORD=marcat_password

# Payment Database
PAYMENT_DB_HOST=localhost
PAYMENT_DB_PORT=5432
PAYMENT_DB_NAME=marcat_payments
PAYMENT_DB_USER=marcat_user
PAYMENT_DB_PASSWORD=marcat_password

# Inventory Database
INVENTORY_DB_HOST=localhost
INVENTORY_DB_PORT=5432
INVENTORY_DB_NAME=marcat_inventory
INVENTORY_DB_USER=marcat_user
INVENTORY_DB_PASSWORD=marcat_password

# Customer Database
CUSTOMER_DB_HOST=localhost
CUSTOMER_DB_PORT=5432
CUSTOMER_DB_NAME=marcat_customers
CUSTOMER_DB_USER=marcat_user
CUSTOMER_DB_PASSWORD=marcat_password

# Review Database
REVIEW_DB_HOST=localhost
REVIEW_DB_PORT=5432
REVIEW_DB_NAME=marcat_reviews
REVIEW_DB_USER=marcat_user
REVIEW_DB_PASSWORD=marcat_password

# Loyalty Database
LOYALTY_DB_HOST=localhost
LOYALTY_DB_PORT=5432
LOYALTY_DB_NAME=marcat_loyalty
LOYALTY_DB_USER=marcat_user
LOYALTY_DB_PASSWORD=marcat_password

# Notification Database
NOTIFICATION_DB_HOST=localhost
NOTIFICATION_DB_PORT=5432
NOTIFICATION_DB_NAME=marcat_notifications
NOTIFICATION_DB_USER=marcat_user
NOTIFICATION_DB_PASSWORD=marcat_password

# Support Database
SUPPORT_DB_HOST=localhost
SUPPORT_DB_PORT=5432
SUPPORT_DB_NAME=marcat_support
SUPPORT_DB_USER=marcat_user
SUPPORT_DB_PASSWORD=marcat_password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# RabbitMQ Configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=marcat
RABBITMQ_PASSWORD=marcat_queue
RABBITMQ_VHOST=/

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Payment Gateway Configuration
# Stripe
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

# PayPal
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_MODE=sandbox

# File Storage Configuration
# AWS S3
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=marcat-assets

# Cloudinary (Alternative)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# External APIs
# Google Maps API
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# AR SDK Configuration
AR_SDK_API_KEY=your-ar-sdk-api-key
AR_SDK_SECRET=your-ar-sdk-secret

# Analytics Configuration
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
MIXPANEL_TOKEN=your-mixpanel-token

# Monitoring & Logging
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:8080
CORS_CREDENTIALS=true

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-in-production

# Localization
DEFAULT_LANGUAGE=en
DEFAULT_CURRENCY=USD
SUPPORTED_LANGUAGES=en,es,fr,de,it
SUPPORTED_CURRENCIES=USD,EUR,GBP,CAD

# Feature Flags
ENABLE_AR_FEATURE=true
ENABLE_LOYALTY_PROGRAM=true
ENABLE_LIVE_CHAT=true
ENABLE_RECOMMENDATIONS=true
ENABLE_MULTI_CURRENCY=true

# Development Tools
DEBUG=marcat:*
SWAGGER_ENABLED=true
