import { database } from '@/config/database';
import { UserSession } from '@/types';
import { logger } from '@/utils/logger';
import crypto from 'crypto';

export class SessionModel {
  /**
   * Create a new session
   */
  static async create(
    userId: string,
    tokenHash: string,
    refreshTokenHash: string,
    deviceInfo?: string,
    ipAddress?: string,
    userAgent?: string,
    expiresAt?: Date
  ): Promise<UserSession> {
    try {
      const query = `
        INSERT INTO user_sessions (
          user_id, token_hash, refresh_token_hash, device_info, 
          ip_address, user_agent, expires_at, is_active
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `;
      
      const defaultExpiresAt = expiresAt || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
      
      const values = [
        userId,
        tokenHash,
        refreshTokenHash,
        deviceInfo || null,
        ipAddress || null,
        userAgent || null,
        defaultExpiresAt,
        true
      ];

      const result = await database.query(query, values);
      return result.rows[0];
    } catch (error) {
      logger.error('Error creating session', { userId, error });
      throw error;
    }
  }

  /**
   * Find session by ID
   */
  static async findById(id: string): Promise<UserSession | null> {
    try {
      const query = 'SELECT * FROM user_sessions WHERE id = $1';
      const result = await database.query(query, [id]);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error finding session by ID', { id, error });
      throw error;
    }
  }

  /**
   * Find active session by token hash
   */
  static async findByTokenHash(tokenHash: string): Promise<UserSession | null> {
    try {
      const query = `
        SELECT * FROM user_sessions 
        WHERE token_hash = $1 AND is_active = true AND expires_at > NOW()
      `;
      const result = await database.query(query, [tokenHash]);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error finding session by token hash', { error });
      throw error;
    }
  }

  /**
   * Find active session by refresh token hash
   */
  static async findByRefreshTokenHash(refreshTokenHash: string): Promise<UserSession | null> {
    try {
      const query = `
        SELECT * FROM user_sessions 
        WHERE refresh_token_hash = $1 AND is_active = true AND expires_at > NOW()
      `;
      const result = await database.query(query, [refreshTokenHash]);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error finding session by refresh token hash', { error });
      throw error;
    }
  }

  /**
   * Update session activity
   */
  static async updateActivity(id: string): Promise<boolean> {
    try {
      const query = `
        UPDATE user_sessions 
        SET last_activity_at = NOW()
        WHERE id = $1 AND is_active = true
      `;
      
      const result = await database.query(query, [id]);
      return result.rowCount > 0;
    } catch (error) {
      logger.error('Error updating session activity', { id, error });
      throw error;
    }
  }

  /**
   * Update session tokens
   */
  static async updateTokens(
    id: string,
    tokenHash: string,
    refreshTokenHash: string,
    expiresAt?: Date
  ): Promise<boolean> {
    try {
      const query = `
        UPDATE user_sessions 
        SET token_hash = $1, refresh_token_hash = $2, expires_at = $3, last_activity_at = NOW()
        WHERE id = $4 AND is_active = true
      `;
      
      const defaultExpiresAt = expiresAt || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
      
      const result = await database.query(query, [tokenHash, refreshTokenHash, defaultExpiresAt, id]);
      return result.rowCount > 0;
    } catch (error) {
      logger.error('Error updating session tokens', { id, error });
      throw error;
    }
  }

  /**
   * Deactivate session
   */
  static async deactivate(id: string): Promise<boolean> {
    try {
      const query = `
        UPDATE user_sessions 
        SET is_active = false
        WHERE id = $1
      `;
      
      const result = await database.query(query, [id]);
      return result.rowCount > 0;
    } catch (error) {
      logger.error('Error deactivating session', { id, error });
      throw error;
    }
  }

  /**
   * Deactivate all user sessions
   */
  static async deactivateAllUserSessions(userId: string, excludeSessionId?: string): Promise<number> {
    try {
      let query = `
        UPDATE user_sessions 
        SET is_active = false
        WHERE user_id = $1 AND is_active = true
      `;
      const params = [userId];
      
      if (excludeSessionId) {
        query += ' AND id != $2';
        params.push(excludeSessionId);
      }
      
      const result = await database.query(query, params);
      return result.rowCount;
    } catch (error) {
      logger.error('Error deactivating all user sessions', { userId, excludeSessionId, error });
      throw error;
    }
  }

  /**
   * Get user's active sessions
   */
  static async getUserActiveSessions(userId: string): Promise<UserSession[]> {
    try {
      const query = `
        SELECT * FROM user_sessions 
        WHERE user_id = $1 AND is_active = true AND expires_at > NOW()
        ORDER BY last_activity_at DESC
      `;
      
      const result = await database.query(query, [userId]);
      return result.rows;
    } catch (error) {
      logger.error('Error getting user active sessions', { userId, error });
      throw error;
    }
  }

  /**
   * Clean up expired sessions
   */
  static async cleanupExpiredSessions(): Promise<number> {
    try {
      const query = `
        UPDATE user_sessions 
        SET is_active = false
        WHERE expires_at <= NOW() AND is_active = true
      `;
      
      const result = await database.query(query);
      logger.info(`Cleaned up ${result.rowCount} expired sessions`);
      return result.rowCount;
    } catch (error) {
      logger.error('Error cleaning up expired sessions', { error });
      throw error;
    }
  }

  /**
   * Delete old inactive sessions
   */
  static async deleteOldSessions(daysOld: number = 30): Promise<number> {
    try {
      const query = `
        DELETE FROM user_sessions 
        WHERE is_active = false AND created_at < NOW() - INTERVAL '${daysOld} days'
      `;
      
      const result = await database.query(query);
      logger.info(`Deleted ${result.rowCount} old sessions`);
      return result.rowCount;
    } catch (error) {
      logger.error('Error deleting old sessions', { daysOld, error });
      throw error;
    }
  }

  /**
   * Generate token hash
   */
  static generateTokenHash(token: string): string {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  /**
   * Get session statistics
   */
  static async getSessionStats(): Promise<{
    totalActive: number;
    totalExpired: number;
    totalInactive: number;
    uniqueUsers: number;
  }> {
    try {
      const query = `
        SELECT 
          COUNT(CASE WHEN is_active = true AND expires_at > NOW() THEN 1 END) as total_active,
          COUNT(CASE WHEN expires_at <= NOW() THEN 1 END) as total_expired,
          COUNT(CASE WHEN is_active = false THEN 1 END) as total_inactive,
          COUNT(DISTINCT user_id) as unique_users
        FROM user_sessions
      `;
      
      const result = await database.query(query);
      const stats = result.rows[0];
      
      return {
        totalActive: parseInt(stats.total_active) || 0,
        totalExpired: parseInt(stats.total_expired) || 0,
        totalInactive: parseInt(stats.total_inactive) || 0,
        uniqueUsers: parseInt(stats.unique_users) || 0,
      };
    } catch (error) {
      logger.error('Error getting session statistics', { error });
      throw error;
    }
  }
}
