import app from './app';
import { config } from '@/config';
import { logger } from '@/utils/logger';
import { database } from '@/config/database';
import { redisClient } from '@/config/redis';
import { SessionModel } from '@/models/Session';
import fs from 'fs';
import path from 'path';

/**
 * Initialize required directories
 */
const initializeDirectories = (): void => {
  const directories = [
    'logs',
    'uploads',
    'uploads/avatars',
  ];

  directories.forEach(dir => {
    const dirPath = path.join(process.cwd(), dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      logger.info(`Created directory: ${dirPath}`);
    }
  });
};

/**
 * Test database connection
 */
const testDatabaseConnection = async (): Promise<void> => {
  try {
    await database.query('SELECT 1');
    logger.info('Database connection established successfully');
  } catch (error) {
    logger.error('Failed to connect to database', { error });
    throw error;
  }
};

/**
 * Test Redis connection
 */
const testRedisConnection = async (): Promise<void> => {
  try {
    await redisClient.ping();
    logger.info('Redis connection established successfully');
  } catch (error) {
    logger.error('Failed to connect to Redis', { error });
    throw error;
  }
};

/**
 * Setup periodic cleanup tasks
 */
const setupCleanupTasks = (): void => {
  // Clean up expired sessions every hour
  setInterval(async () => {
    try {
      const cleanedCount = await SessionModel.cleanupExpiredSessions();
      if (cleanedCount > 0) {
        logger.info(`Cleaned up ${cleanedCount} expired sessions`);
      }
    } catch (error) {
      logger.error('Failed to cleanup expired sessions', { error });
    }
  }, 60 * 60 * 1000); // 1 hour

  // Delete old inactive sessions every 24 hours
  setInterval(async () => {
    try {
      const deletedCount = await SessionModel.deleteOldSessions(30); // 30 days old
      if (deletedCount > 0) {
        logger.info(`Deleted ${deletedCount} old inactive sessions`);
      }
    } catch (error) {
      logger.error('Failed to delete old sessions', { error });
    }
  }, 24 * 60 * 60 * 1000); // 24 hours

  logger.info('Cleanup tasks scheduled successfully');
};

/**
 * Start the server
 */
const startServer = async (): Promise<void> => {
  try {
    // Initialize directories
    initializeDirectories();

    // Test connections
    await testDatabaseConnection();
    await testRedisConnection();

    // Setup cleanup tasks
    setupCleanupTasks();

    // Start server
    const server = app.listen(config.server.port, config.server.host, () => {
      logger.info(`🚀 Auth Service started successfully`, {
        service: config.service.name,
        version: config.service.version,
        environment: config.server.nodeEnv,
        host: config.server.host,
        port: config.server.port,
        url: `http://${config.server.host}:${config.server.port}`,
      });
    });

    // Graceful shutdown
    const gracefulShutdown = (signal: string) => {
      logger.info(`${signal} received, starting graceful shutdown`);
      
      server.close(async (err) => {
        if (err) {
          logger.error('Error during server shutdown', { error: err });
          process.exit(1);
        }

        try {
          // Close database connections
          await database.end();
          logger.info('Database connections closed');

          // Close Redis connection
          await redisClient.quit();
          logger.info('Redis connection closed');

          logger.info('Graceful shutdown completed');
          process.exit(0);
        } catch (error) {
          logger.error('Error during graceful shutdown', { error });
          process.exit(1);
        }
      });

      // Force shutdown after 30 seconds
      setTimeout(() => {
        logger.error('Forced shutdown after timeout');
        process.exit(1);
      }, 30000);
    };

    // Handle shutdown signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception', { error });
      gracefulShutdown('UNCAUGHT_EXCEPTION');
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection', { reason, promise });
      gracefulShutdown('UNHANDLED_REJECTION');
    });

  } catch (error) {
    logger.error('Failed to start server', { error });
    process.exit(1);
  }
};

// Start the server
startServer();
