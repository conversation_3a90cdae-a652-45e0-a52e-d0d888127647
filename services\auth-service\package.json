{"name": "@marcat/auth-service", "version": "1.0.0", "description": "Authentication and User Management Service for Marcat E-commerce Platform", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "migrate": "ts-node src/database/migrate.ts", "seed": "ts-node src/database/seed.ts"}, "keywords": ["authentication", "jwt", "microservice", "ecommerce", "nodejs", "typescript"], "author": "Marcat Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "nodemailer": "^6.9.7", "redis": "^4.6.10", "uuid": "^9.0.1", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "cookie-parser": "^1.4.6", "multer": "^1.4.5-lts.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/pg": "^8.10.9", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.5", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@types/cookie-parser": "^1.4.6", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}