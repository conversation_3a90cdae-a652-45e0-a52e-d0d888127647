-- Migration: Create Inventory Database Tables
-- Database: marcat_inventory
-- Created: 2025-07-03

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Inventory table
CREATE TABLE inventory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    variant_id UUID UNIQUE NOT NULL, -- References product_variants.id from product database
    store_id UUID NOT NULL, -- References stores.id from store database
    
    -- Stock Quantities
    quantity_available INTEGER NOT NULL DEFAULT 0 CHECK (quantity_available >= 0),
    quantity_reserved INTEGER NOT NULL DEFAULT 0 CHECK (quantity_reserved >= 0),
    quantity_sold INTEGER NOT NULL DEFAULT 0 CHECK (quantity_sold >= 0),
    quantity_damaged INTEGER NOT NULL DEFAULT 0 CHECK (quantity_damaged >= 0),
    quantity_returned INTEGER NOT NULL DEFAULT 0 CHECK (quantity_returned >= 0),
    
    -- Stock Management
    reorder_point INTEGER DEFAULT 10 CHECK (reorder_point >= 0),
    max_stock_level INTEGER CHECK (max_stock_level IS NULL OR max_stock_level > 0),
    min_stock_level INTEGER DEFAULT 0 CHECK (min_stock_level >= 0),
    
    -- Cost Information
    unit_cost DECIMAL(10,2),
    total_cost DECIMAL(12,2),
    
    -- Location Information
    warehouse_location VARCHAR(100),
    bin_location VARCHAR(50),
    
    -- Stock Status
    is_tracked BOOLEAN DEFAULT TRUE,
    allow_backorder BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    last_restocked_at TIMESTAMP,
    last_sold_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Stock Movements table
CREATE TABLE stock_movements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    variant_id UUID NOT NULL, -- References product_variants.id
    store_id UUID NOT NULL, -- References stores.id
    
    -- Movement Details
    movement_type VARCHAR(20) NOT NULL CHECK (movement_type IN ('in', 'out', 'adjustment', 'transfer', 'return', 'damage')),
    quantity INTEGER NOT NULL,
    
    -- Reference Information
    reference_type VARCHAR(50), -- 'order', 'restock', 'adjustment', 'transfer', 'return'
    reference_id UUID,
    
    -- Movement Context
    reason VARCHAR(100),
    notes TEXT,
    
    -- Cost Information
    unit_cost DECIMAL(10,2),
    total_cost DECIMAL(12,2),
    
    -- Location Information
    from_location VARCHAR(100),
    to_location VARCHAR(100),
    
    -- User Information
    created_by UUID, -- References users.id
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Low Stock Alerts
CREATE TABLE low_stock_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    variant_id UUID NOT NULL, -- References product_variants.id
    store_id UUID NOT NULL, -- References stores.id
    
    -- Alert Details
    current_quantity INTEGER NOT NULL,
    reorder_point INTEGER NOT NULL,
    alert_type VARCHAR(20) DEFAULT 'low_stock' CHECK (alert_type IN ('low_stock', 'out_of_stock', 'overstock')),
    
    -- Alert Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'acknowledged', 'resolved')),
    
    -- Alert Actions
    acknowledged_by UUID, -- References users.id
    acknowledged_at TIMESTAMP,
    resolved_at TIMESTAMP,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Inventory Adjustments
CREATE TABLE inventory_adjustments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    variant_id UUID NOT NULL, -- References product_variants.id
    store_id UUID NOT NULL, -- References stores.id
    
    -- Adjustment Details
    adjustment_type VARCHAR(20) NOT NULL CHECK (adjustment_type IN ('increase', 'decrease', 'set')),
    quantity_before INTEGER NOT NULL,
    quantity_after INTEGER NOT NULL,
    adjustment_quantity INTEGER NOT NULL,
    
    -- Adjustment Reason
    reason VARCHAR(100) NOT NULL,
    notes TEXT,
    
    -- Cost Impact
    cost_impact DECIMAL(12,2),
    
    -- Approval Information
    requires_approval BOOLEAN DEFAULT FALSE,
    approved_by UUID, -- References users.id
    approved_at TIMESTAMP,
    
    -- User Information
    created_by UUID NOT NULL, -- References users.id
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Inventory Transfers (between locations/warehouses)
CREATE TABLE inventory_transfers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transfer_number VARCHAR(50) UNIQUE NOT NULL,
    
    -- Transfer Details
    from_store_id UUID NOT NULL, -- References stores.id
    to_store_id UUID NOT NULL, -- References stores.id
    from_location VARCHAR(100),
    to_location VARCHAR(100),
    
    -- Transfer Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in_transit', 'completed', 'cancelled')),
    
    -- Transfer Information
    total_items INTEGER DEFAULT 0,
    total_quantity INTEGER DEFAULT 0,
    notes TEXT,
    
    -- User Information
    created_by UUID NOT NULL, -- References users.id
    approved_by UUID, -- References users.id
    received_by UUID, -- References users.id
    
    -- Timestamps
    approved_at TIMESTAMP,
    shipped_at TIMESTAMP,
    received_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Inventory Transfer Items
CREATE TABLE inventory_transfer_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transfer_id UUID REFERENCES inventory_transfers(id) ON DELETE CASCADE,
    variant_id UUID NOT NULL, -- References product_variants.id
    
    -- Transfer Item Details
    quantity_requested INTEGER NOT NULL CHECK (quantity_requested > 0),
    quantity_shipped INTEGER DEFAULT 0 CHECK (quantity_shipped >= 0),
    quantity_received INTEGER DEFAULT 0 CHECK (quantity_received >= 0),
    
    -- Cost Information
    unit_cost DECIMAL(10,2),
    total_cost DECIMAL(12,2),
    
    -- Item Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'shipped', 'received', 'damaged')),
    
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Inventory Reservations (for pending orders)
CREATE TABLE inventory_reservations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    variant_id UUID NOT NULL, -- References product_variants.id
    store_id UUID NOT NULL, -- References stores.id
    
    -- Reservation Details
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    
    -- Reference Information
    reference_type VARCHAR(50) NOT NULL, -- 'order', 'cart', 'quote'
    reference_id UUID NOT NULL,
    
    -- Reservation Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'fulfilled', 'cancelled', 'expired')),
    
    -- Expiration
    expires_at TIMESTAMP,
    
    -- User Information
    reserved_for UUID, -- References users.id
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Inventory Snapshots (for historical tracking)
CREATE TABLE inventory_snapshots (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    variant_id UUID NOT NULL, -- References product_variants.id
    store_id UUID NOT NULL, -- References stores.id
    
    -- Snapshot Data
    snapshot_date DATE NOT NULL,
    quantity_available INTEGER NOT NULL,
    quantity_reserved INTEGER NOT NULL,
    quantity_sold INTEGER NOT NULL,
    total_value DECIMAL(12,2),
    
    -- Snapshot Type
    snapshot_type VARCHAR(20) DEFAULT 'daily' CHECK (snapshot_type IN ('daily', 'weekly', 'monthly', 'manual')),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(variant_id, store_id, snapshot_date, snapshot_type)
);

-- Indexes for performance
CREATE INDEX idx_inventory_variant_id ON inventory(variant_id);
CREATE INDEX idx_inventory_store_id ON inventory(store_id);
CREATE INDEX idx_inventory_low_stock ON inventory(quantity_available) WHERE quantity_available <= reorder_point;
CREATE INDEX idx_inventory_out_of_stock ON inventory(quantity_available) WHERE quantity_available = 0;

CREATE INDEX idx_stock_movements_variant_id ON stock_movements(variant_id);
CREATE INDEX idx_stock_movements_store_id ON stock_movements(store_id);
CREATE INDEX idx_stock_movements_type ON stock_movements(movement_type);
CREATE INDEX idx_stock_movements_reference ON stock_movements(reference_type, reference_id);
CREATE INDEX idx_stock_movements_created_at ON stock_movements(created_at DESC);

CREATE INDEX idx_low_stock_alerts_variant_id ON low_stock_alerts(variant_id);
CREATE INDEX idx_low_stock_alerts_store_id ON low_stock_alerts(store_id);
CREATE INDEX idx_low_stock_alerts_status ON low_stock_alerts(status);
CREATE INDEX idx_low_stock_alerts_type ON low_stock_alerts(alert_type);

CREATE INDEX idx_inventory_adjustments_variant_id ON inventory_adjustments(variant_id);
CREATE INDEX idx_inventory_adjustments_store_id ON inventory_adjustments(store_id);
CREATE INDEX idx_inventory_adjustments_created_by ON inventory_adjustments(created_by);

CREATE INDEX idx_inventory_transfers_from_store ON inventory_transfers(from_store_id);
CREATE INDEX idx_inventory_transfers_to_store ON inventory_transfers(to_store_id);
CREATE INDEX idx_inventory_transfers_status ON inventory_transfers(status);

CREATE INDEX idx_inventory_reservations_variant_id ON inventory_reservations(variant_id);
CREATE INDEX idx_inventory_reservations_reference ON inventory_reservations(reference_type, reference_id);
CREATE INDEX idx_inventory_reservations_status ON inventory_reservations(status);
CREATE INDEX idx_inventory_reservations_expires_at ON inventory_reservations(expires_at);

CREATE INDEX idx_inventory_snapshots_variant_store ON inventory_snapshots(variant_id, store_id);
CREATE INDEX idx_inventory_snapshots_date ON inventory_snapshots(snapshot_date DESC);

-- Function to update inventory quantities
CREATE OR REPLACE FUNCTION update_inventory_quantity(
    p_variant_id UUID,
    p_store_id UUID,
    p_movement_type VARCHAR,
    p_quantity INTEGER,
    p_reference_type VARCHAR DEFAULT NULL,
    p_reference_id UUID DEFAULT NULL,
    p_notes TEXT DEFAULT NULL,
    p_created_by UUID DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
    current_available INTEGER;
    current_reserved INTEGER;
BEGIN
    -- Get current quantities
    SELECT quantity_available, quantity_reserved 
    INTO current_available, current_reserved
    FROM inventory 
    WHERE variant_id = p_variant_id AND store_id = p_store_id;
    
    -- If inventory record doesn't exist, create it
    IF NOT FOUND THEN
        INSERT INTO inventory (variant_id, store_id, quantity_available, quantity_reserved)
        VALUES (p_variant_id, p_store_id, 0, 0);
        current_available := 0;
        current_reserved := 0;
    END IF;
    
    -- Update quantities based on movement type
    CASE p_movement_type
        WHEN 'in' THEN
            UPDATE inventory 
            SET quantity_available = quantity_available + p_quantity,
                last_restocked_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE variant_id = p_variant_id AND store_id = p_store_id;
            
        WHEN 'out' THEN
            UPDATE inventory 
            SET quantity_available = GREATEST(0, quantity_available - p_quantity),
                quantity_sold = quantity_sold + p_quantity,
                last_sold_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE variant_id = p_variant_id AND store_id = p_store_id;
            
        WHEN 'reserve' THEN
            UPDATE inventory 
            SET quantity_available = GREATEST(0, quantity_available - p_quantity),
                quantity_reserved = quantity_reserved + p_quantity,
                updated_at = CURRENT_TIMESTAMP
            WHERE variant_id = p_variant_id AND store_id = p_store_id;
            
        WHEN 'unreserve' THEN
            UPDATE inventory 
            SET quantity_available = quantity_available + p_quantity,
                quantity_reserved = GREATEST(0, quantity_reserved - p_quantity),
                updated_at = CURRENT_TIMESTAMP
            WHERE variant_id = p_variant_id AND store_id = p_store_id;
    END CASE;
    
    -- Record stock movement
    INSERT INTO stock_movements (
        variant_id, store_id, movement_type, quantity, 
        reference_type, reference_id, notes, created_by
    ) VALUES (
        p_variant_id, p_store_id, p_movement_type, p_quantity,
        p_reference_type, p_reference_id, p_notes, p_created_by
    );
    
    -- Check for low stock alerts
    PERFORM check_low_stock_alert(p_variant_id, p_store_id);
END;
$$ LANGUAGE plpgsql;

-- Function to check and create low stock alerts
CREATE OR REPLACE FUNCTION check_low_stock_alert(
    p_variant_id UUID,
    p_store_id UUID
)
RETURNS VOID AS $$
DECLARE
    current_quantity INTEGER;
    reorder_level INTEGER;
    existing_alert_id UUID;
BEGIN
    -- Get current inventory levels
    SELECT quantity_available, reorder_point
    INTO current_quantity, reorder_level
    FROM inventory
    WHERE variant_id = p_variant_id AND store_id = p_store_id;
    
    -- Check if there's already an active alert
    SELECT id INTO existing_alert_id
    FROM low_stock_alerts
    WHERE variant_id = p_variant_id 
      AND store_id = p_store_id 
      AND status = 'active';
    
    -- Create alert if quantity is at or below reorder point and no active alert exists
    IF current_quantity <= reorder_level AND existing_alert_id IS NULL THEN
        INSERT INTO low_stock_alerts (
            variant_id, store_id, current_quantity, reorder_point,
            alert_type
        ) VALUES (
            p_variant_id, p_store_id, current_quantity, reorder_level,
            CASE WHEN current_quantity = 0 THEN 'out_of_stock' ELSE 'low_stock' END
        );
    
    -- Resolve alert if quantity is above reorder point and alert exists
    ELSIF current_quantity > reorder_level AND existing_alert_id IS NOT NULL THEN
        UPDATE low_stock_alerts
        SET status = 'resolved', resolved_at = CURRENT_TIMESTAMP
        WHERE id = existing_alert_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_inventory_updated_at BEFORE UPDATE ON inventory
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_inventory_transfers_updated_at BEFORE UPDATE ON inventory_transfers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_inventory_reservations_updated_at BEFORE UPDATE ON inventory_reservations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
