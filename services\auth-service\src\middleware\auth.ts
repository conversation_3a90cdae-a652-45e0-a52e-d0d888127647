import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest, ApiResponse } from '@/types';
import { JWTUtil } from '@/utils/jwt';
import { UserModel } from '@/models/User';
import { SessionModel } from '@/models/Session';
import { RoleModel } from '@/models/Role';
import { logger } from '@/utils/logger';

/**
 * Authentication middleware
 * Verifies JWT token and attaches user to request
 */
export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = JWTUtil.extractTokenFromHeader(authHeader);

    if (!token) {
      const response: ApiResponse = {
        success: false,
        message: 'Access token required',
      };
      res.status(401).json(response);
      return;
    }

    // Verify token
    const payload = JWTUtil.verifyAccessToken(token);
    
    // Check if session is still active
    const tokenHash = SessionModel.generateTokenHash(token);
    const session = await SessionModel.findByTokenHash(tokenHash);
    
    if (!session) {
      const response: ApiResponse = {
        success: false,
        message: 'Invalid or expired session',
      };
      res.status(401).json(response);
      return;
    }

    // Get user details
    const user = await UserModel.findById(payload.userId);
    
    if (!user || user.status !== 'active') {
      const response: ApiResponse = {
        success: false,
        message: 'User not found or inactive',
      };
      res.status(401).json(response);
      return;
    }

    // Get user roles
    const roles = await RoleModel.getUserRoles(user.id);

    // Update session activity
    await SessionModel.updateActivity(session.id);

    // Attach to request
    req.user = user;
    req.session = session;
    req.roles = roles;

    next();
  } catch (error) {
    logger.error('Authentication middleware error', { error });
    
    let message = 'Authentication failed';
    if (error instanceof Error) {
      if (error.message.includes('expired')) {
        message = 'Access token expired';
      } else if (error.message.includes('invalid')) {
        message = 'Invalid access token';
      }
    }

    const response: ApiResponse = {
      success: false,
      message,
    };
    res.status(401).json(response);
  }
};

/**
 * Optional authentication middleware
 * Attaches user to request if token is valid, but doesn't require it
 */
export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = JWTUtil.extractTokenFromHeader(authHeader);

    if (!token) {
      next();
      return;
    }

    // Try to verify token
    const payload = JWTUtil.verifyAccessToken(token);
    
    // Check if session is still active
    const tokenHash = SessionModel.generateTokenHash(token);
    const session = await SessionModel.findByTokenHash(tokenHash);
    
    if (!session) {
      next();
      return;
    }

    // Get user details
    const user = await UserModel.findById(payload.userId);
    
    if (!user || user.status !== 'active') {
      next();
      return;
    }

    // Get user roles
    const roles = await RoleModel.getUserRoles(user.id);

    // Update session activity
    await SessionModel.updateActivity(session.id);

    // Attach to request
    req.user = user;
    req.session = session;
    req.roles = roles;

    next();
  } catch (error) {
    // If token verification fails, just continue without user
    logger.debug('Optional auth failed', { error: error instanceof Error ? error.message : error });
    next();
  }
};

/**
 * Authorization middleware factory
 * Checks if user has required roles or permissions
 */
export const authorize = (requiredRoles: string[] = [], requiredPermissions: string[] = []) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user || !req.roles) {
        const response: ApiResponse = {
          success: false,
          message: 'Authentication required',
        };
        res.status(401).json(response);
        return;
      }

      const userRoles = req.roles.map(role => role.name);
      const userPermissions = req.roles.flatMap(role => role.permissions);

      // Check roles
      if (requiredRoles.length > 0) {
        const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));
        if (!hasRequiredRole) {
          const response: ApiResponse = {
            success: false,
            message: 'Insufficient permissions - required roles not found',
          };
          res.status(403).json(response);
          return;
        }
      }

      // Check permissions
      if (requiredPermissions.length > 0) {
        const hasRequiredPermission = requiredPermissions.some(permission => 
          userPermissions.includes(permission)
        );
        if (!hasRequiredPermission) {
          const response: ApiResponse = {
            success: false,
            message: 'Insufficient permissions - required permissions not found',
          };
          res.status(403).json(response);
          return;
        }
      }

      next();
    } catch (error) {
      logger.error('Authorization middleware error', { error });
      const response: ApiResponse = {
        success: false,
        message: 'Authorization failed',
      };
      res.status(500).json(response);
    }
  };
};

/**
 * Admin only middleware
 */
export const adminOnly = authorize(['admin']);

/**
 * Store owner or admin middleware
 */
export const storeOwnerOrAdmin = authorize(['store_owner', 'admin']);

/**
 * Customer or higher middleware
 */
export const customerOrHigher = authorize(['customer', 'store_owner', 'admin']);

/**
 * Self or admin middleware
 * Allows users to access their own resources or admins to access any
 */
export const selfOrAdmin = (userIdParam: string = 'userId') => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user || !req.roles) {
        const response: ApiResponse = {
          success: false,
          message: 'Authentication required',
        };
        res.status(401).json(response);
        return;
      }

      const userRoles = req.roles.map(role => role.name);
      const isAdmin = userRoles.includes('admin');
      const targetUserId = req.params[userIdParam];

      if (isAdmin || req.user.id === targetUserId) {
        next();
        return;
      }

      const response: ApiResponse = {
        success: false,
        message: 'Access denied - can only access own resources',
      };
      res.status(403).json(response);
    } catch (error) {
      logger.error('Self or admin middleware error', { error });
      const response: ApiResponse = {
        success: false,
        message: 'Authorization failed',
      };
      res.status(500).json(response);
    }
  };
};
