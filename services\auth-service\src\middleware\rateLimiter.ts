import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import { ApiResponse } from '@/types';
import { config } from '@/config';
import { logger } from '@/utils/logger';

/**
 * Create rate limiter with custom options
 */
const createRateLimiter = (options: {
  windowMs: number;
  max: number;
  message: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}) => {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    skipSuccessfulRequests: options.skipSuccessfulRequests || false,
    skipFailedRequests: options.skipFailedRequests || false,
    handler: (req: Request, res: Response) => {
      logger.warn('Rate limit exceeded', {
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        path: req.path,
        method: req.method,
      });

      const response: ApiResponse = {
        success: false,
        message: options.message,
      };

      res.status(429).j<PERSON>(response);
    },
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  });
};

/**
 * General API rate limiter
 * 100 requests per 15 minutes
 */
export const generalLimiter = createRateLimiter({
  windowMs: config.security.rateLimitWindowMs, // 15 minutes
  max: config.security.rateLimitMaxRequests, // 100 requests
  message: 'Too many requests from this IP, please try again later.',
});

/**
 * Authentication rate limiter
 * 5 login attempts per 15 minutes
 */
export const authLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts
  message: 'Too many authentication attempts, please try again later.',
  skipSuccessfulRequests: true, // Don't count successful requests
});

/**
 * Registration rate limiter
 * 3 registrations per hour per IP
 */
export const registerLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 registrations
  message: 'Too many registration attempts, please try again later.',
});

/**
 * Password reset rate limiter
 * 3 password reset requests per hour
 */
export const passwordResetLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 requests
  message: 'Too many password reset requests, please try again later.',
});

/**
 * Email verification rate limiter
 * 5 verification emails per hour
 */
export const emailVerificationLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // 5 requests
  message: 'Too many email verification requests, please try again later.',
});

/**
 * Token refresh rate limiter
 * 10 refresh attempts per 15 minutes
 */
export const refreshLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 attempts
  message: 'Too many token refresh attempts, please try again later.',
});

/**
 * Password change rate limiter
 * 3 password changes per hour
 */
export const changePasswordLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 changes
  message: 'Too many password change attempts, please try again later.',
});

/**
 * Profile update rate limiter
 * 10 profile updates per hour
 */
export const profileUpdateLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 updates
  message: 'Too many profile update attempts, please try again later.',
});

/**
 * File upload rate limiter
 * 20 uploads per hour
 */
export const uploadLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 20, // 20 uploads
  message: 'Too many file upload attempts, please try again later.',
});

/**
 * Admin operations rate limiter
 * 100 admin operations per 15 minutes
 */
export const adminLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 operations
  message: 'Too many admin operations, please try again later.',
});

/**
 * Search rate limiter
 * 30 searches per minute
 */
export const searchLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 searches
  message: 'Too many search requests, please try again later.',
});

/**
 * Export rate limiters for specific endpoints
 */
export const rateLimiter = {
  general: generalLimiter,
  auth: authLimiter,
  register: registerLimiter,
  login: authLimiter,
  passwordReset: passwordResetLimiter,
  emailVerification: emailVerificationLimiter,
  refresh: refreshLimiter,
  changePassword: changePasswordLimiter,
  profileUpdate: profileUpdateLimiter,
  upload: uploadLimiter,
  admin: adminLimiter,
  search: searchLimiter,
};

export default rateLimiter;
