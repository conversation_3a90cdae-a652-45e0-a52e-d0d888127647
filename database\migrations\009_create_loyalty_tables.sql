-- Migration: Create Loyalty Database Tables
-- Database: marcat_loyalty
-- Created: 2025-07-03

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Loyalty Tiers
CREATE TABLE loyalty_tiers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) UNIQUE NOT NULL,
    slug VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    
    -- Tier Requirements
    min_points INTEGER NOT NULL DEFAULT 0,
    min_orders INTEGER DEFAULT 0,
    min_spend DECIMAL(10,2) DEFAULT 0.00,
    
    -- Tier Benefits
    points_multiplier DECIMAL(3,2) DEFAULT 1.00, -- 1.5x points, etc.
    discount_percentage DECIMAL(5,2) DEFAULT 0.00,
    free_shipping BOOLEAN DEFAULT FALSE,
    early_access BOOLEAN DEFAULT FALSE,
    
    -- Tier Settings
    color VARCHAR(7), -- Hex color code
    icon_url TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Loyalty Points
CREATE TABLE loyalty_points (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id from auth database
    
    -- Points Balance
    total_points INTEGER NOT NULL DEFAULT 0,
    available_points INTEGER NOT NULL DEFAULT 0,
    pending_points INTEGER NOT NULL DEFAULT 0,
    redeemed_points INTEGER NOT NULL DEFAULT 0,
    expired_points INTEGER NOT NULL DEFAULT 0,
    
    -- Current Tier
    current_tier_id UUID REFERENCES loyalty_tiers(id),
    tier_progress INTEGER DEFAULT 0, -- Points towards next tier
    
    -- Lifetime Stats
    lifetime_points_earned INTEGER DEFAULT 0,
    lifetime_points_redeemed INTEGER DEFAULT 0,
    lifetime_orders INTEGER DEFAULT 0,
    lifetime_spend DECIMAL(12,2) DEFAULT 0.00,
    
    -- Tier History
    tier_achieved_at TIMESTAMP,
    previous_tier_id UUID REFERENCES loyalty_tiers(id),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Points Transactions
CREATE TABLE points_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id
    
    -- Transaction Details
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('earned', 'redeemed', 'expired', 'adjusted', 'bonus')),
    points INTEGER NOT NULL,
    
    -- Transaction Context
    reference_type VARCHAR(50), -- 'order', 'review', 'referral', 'bonus', 'adjustment'
    reference_id UUID,
    
    -- Transaction Description
    description TEXT NOT NULL,
    notes TEXT,
    
    -- Expiration (for earned points)
    expires_at TIMESTAMP,
    
    -- Processing Status
    status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'cancelled', 'expired')),
    
    -- Admin Information (for adjustments)
    created_by UUID, -- References users.id (admin who created adjustment)
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Rewards Catalog
CREATE TABLE rewards (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Reward Details
    name VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    
    -- Reward Type
    reward_type VARCHAR(20) NOT NULL CHECK (reward_type IN ('discount', 'free_shipping', 'product', 'experience', 'cashback')),
    
    -- Reward Value
    points_required INTEGER NOT NULL CHECK (points_required > 0),
    monetary_value DECIMAL(10,2),
    discount_percentage DECIMAL(5,2),
    discount_amount DECIMAL(10,2),
    
    -- Reward Constraints
    min_order_amount DECIMAL(10,2),
    max_uses_per_user INTEGER,
    max_total_uses INTEGER,
    
    -- Product Rewards (if applicable)
    product_id UUID, -- References products.id
    variant_id UUID, -- References product_variants.id
    
    -- Reward Settings
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    
    -- Availability
    starts_at TIMESTAMP,
    ends_at TIMESTAMP,
    
    -- Usage Stats
    total_redeemed INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Reward Redemptions
CREATE TABLE reward_redemptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id
    reward_id UUID REFERENCES rewards(id),
    
    -- Redemption Details
    points_used INTEGER NOT NULL,
    
    -- Redemption Code (for discount rewards)
    redemption_code VARCHAR(50) UNIQUE,
    
    -- Redemption Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'used', 'expired', 'cancelled')),
    
    -- Usage Information
    order_id UUID, -- References orders.id (when used)
    used_at TIMESTAMP,
    expires_at TIMESTAMP,
    
    -- Redemption Value
    discount_amount DECIMAL(10,2),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Referral Program
CREATE TABLE referrals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    referrer_id UUID NOT NULL, -- References users.id (person making referral)
    referee_id UUID, -- References users.id (person being referred, NULL until they sign up)
    
    -- Referral Details
    referral_code VARCHAR(20) UNIQUE NOT NULL,
    referee_email VARCHAR(255),
    
    -- Referral Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'rewarded', 'expired')),
    
    -- Rewards
    referrer_points INTEGER DEFAULT 0,
    referee_points INTEGER DEFAULT 0,
    referrer_discount DECIMAL(10,2) DEFAULT 0.00,
    referee_discount DECIMAL(10,2) DEFAULT 0.00,
    
    -- Completion Requirements
    requires_purchase BOOLEAN DEFAULT TRUE,
    min_purchase_amount DECIMAL(10,2) DEFAULT 0.00,
    
    -- Timestamps
    completed_at TIMESTAMP, -- When referee makes qualifying action
    rewarded_at TIMESTAMP, -- When rewards are given
    expires_at TIMESTAMP,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Loyalty Events (for tracking user actions that earn points)
CREATE TABLE loyalty_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id
    
    -- Event Details
    event_type VARCHAR(50) NOT NULL, -- 'order_placed', 'review_written', 'referral_completed', etc.
    event_data JSONB,
    
    -- Points Calculation
    base_points INTEGER DEFAULT 0,
    bonus_points INTEGER DEFAULT 0,
    multiplier DECIMAL(3,2) DEFAULT 1.00,
    total_points INTEGER NOT NULL,
    
    -- Processing Status
    status VARCHAR(20) DEFAULT 'processed' CHECK (status IN ('pending', 'processed', 'failed')),
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Reference
    reference_type VARCHAR(50),
    reference_id UUID,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Loyalty Campaigns (special promotions)
CREATE TABLE loyalty_campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Campaign Details
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Campaign Type
    campaign_type VARCHAR(20) NOT NULL CHECK (campaign_type IN ('points_multiplier', 'bonus_points', 'tier_boost', 'special_reward')),
    
    -- Campaign Rules
    rules JSONB NOT NULL, -- Campaign-specific rules and conditions
    
    -- Campaign Rewards
    points_multiplier DECIMAL(3,2) DEFAULT 1.00,
    bonus_points INTEGER DEFAULT 0,
    
    -- Campaign Settings
    is_active BOOLEAN DEFAULT TRUE,
    max_participants INTEGER,
    current_participants INTEGER DEFAULT 0,
    
    -- Campaign Period
    starts_at TIMESTAMP NOT NULL,
    ends_at TIMESTAMP NOT NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Campaign Participations
CREATE TABLE campaign_participations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID REFERENCES loyalty_campaigns(id) ON DELETE CASCADE,
    user_id UUID NOT NULL, -- References users.id
    
    -- Participation Details
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    points_earned INTEGER DEFAULT 0,
    
    -- Participation Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'disqualified')),
    
    PRIMARY KEY (campaign_id, user_id)
);

-- Indexes for performance
CREATE INDEX idx_loyalty_points_user_id ON loyalty_points(user_id);
CREATE INDEX idx_loyalty_points_tier_id ON loyalty_points(current_tier_id);

CREATE INDEX idx_points_transactions_user_id ON points_transactions(user_id);
CREATE INDEX idx_points_transactions_type ON points_transactions(transaction_type);
CREATE INDEX idx_points_transactions_reference ON points_transactions(reference_type, reference_id);
CREATE INDEX idx_points_transactions_created_at ON points_transactions(created_at DESC);
CREATE INDEX idx_points_transactions_expires_at ON points_transactions(expires_at);

CREATE INDEX idx_rewards_type ON rewards(reward_type);
CREATE INDEX idx_rewards_active ON rewards(is_active);
CREATE INDEX idx_rewards_featured ON rewards(is_featured);
CREATE INDEX idx_rewards_points_required ON rewards(points_required);

CREATE INDEX idx_reward_redemptions_user_id ON reward_redemptions(user_id);
CREATE INDEX idx_reward_redemptions_reward_id ON reward_redemptions(reward_id);
CREATE INDEX idx_reward_redemptions_status ON reward_redemptions(status);
CREATE INDEX idx_reward_redemptions_code ON reward_redemptions(redemption_code);

CREATE INDEX idx_referrals_referrer_id ON referrals(referrer_id);
CREATE INDEX idx_referrals_referee_id ON referrals(referee_id);
CREATE INDEX idx_referrals_code ON referrals(referral_code);
CREATE INDEX idx_referrals_status ON referrals(status);

CREATE INDEX idx_loyalty_events_user_id ON loyalty_events(user_id);
CREATE INDEX idx_loyalty_events_type ON loyalty_events(event_type);
CREATE INDEX idx_loyalty_events_reference ON loyalty_events(reference_type, reference_id);

-- Insert default loyalty tiers
INSERT INTO loyalty_tiers (name, slug, description, min_points, points_multiplier, color, sort_order) VALUES
('Bronze', 'bronze', 'Welcome to Marcat! Start earning points with every purchase.', 0, 1.00, '#CD7F32', 1),
('Silver', 'silver', 'Unlock 1.25x points and exclusive offers.', 1000, 1.25, '#C0C0C0', 2),
('Gold', 'gold', 'Enjoy 1.5x points, free shipping, and early access.', 5000, 1.50, '#FFD700', 3),
('Platinum', 'platinum', 'Premium benefits with 2x points and VIP support.', 15000, 2.00, '#E5E4E2', 4);

-- Function to calculate user tier
CREATE OR REPLACE FUNCTION calculate_user_tier(p_user_id UUID)
RETURNS UUID AS $$
DECLARE
    user_points INTEGER;
    tier_id UUID;
BEGIN
    -- Get user's total points
    SELECT total_points INTO user_points
    FROM loyalty_points
    WHERE user_id = p_user_id;
    
    -- If no loyalty record exists, return Bronze tier
    IF user_points IS NULL THEN
        SELECT id INTO tier_id
        FROM loyalty_tiers
        WHERE slug = 'bronze';
        RETURN tier_id;
    END IF;
    
    -- Find appropriate tier
    SELECT id INTO tier_id
    FROM loyalty_tiers
    WHERE min_points <= user_points
      AND is_active = TRUE
    ORDER BY min_points DESC
    LIMIT 1;
    
    RETURN tier_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update user loyalty status
CREATE OR REPLACE FUNCTION update_user_loyalty(p_user_id UUID)
RETURNS VOID AS $$
DECLARE
    new_tier_id UUID;
    current_tier_id UUID;
BEGIN
    -- Calculate new tier
    new_tier_id := calculate_user_tier(p_user_id);
    
    -- Get current tier
    SELECT current_tier_id INTO current_tier_id
    FROM loyalty_points
    WHERE user_id = p_user_id;
    
    -- Update tier if changed
    IF new_tier_id != current_tier_id OR current_tier_id IS NULL THEN
        UPDATE loyalty_points
        SET current_tier_id = new_tier_id,
            previous_tier_id = current_tier_id,
            tier_achieved_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = p_user_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to add points
CREATE OR REPLACE FUNCTION add_points(
    p_user_id UUID,
    p_points INTEGER,
    p_transaction_type VARCHAR,
    p_description TEXT,
    p_reference_type VARCHAR DEFAULT NULL,
    p_reference_id UUID DEFAULT NULL,
    p_expires_at TIMESTAMP DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    -- Ensure user has loyalty record
    INSERT INTO loyalty_points (user_id, total_points, available_points)
    VALUES (p_user_id, 0, 0)
    ON CONFLICT (user_id) DO NOTHING;
    
    -- Add points transaction
    INSERT INTO points_transactions (
        user_id, transaction_type, points, description,
        reference_type, reference_id, expires_at
    ) VALUES (
        p_user_id, p_transaction_type, p_points, p_description,
        p_reference_type, p_reference_id, p_expires_at
    );
    
    -- Update user points balance
    UPDATE loyalty_points
    SET total_points = total_points + p_points,
        available_points = available_points + p_points,
        lifetime_points_earned = lifetime_points_earned + p_points,
        updated_at = CURRENT_TIMESTAMP
    WHERE user_id = p_user_id;
    
    -- Update user tier
    PERFORM update_user_loyalty(p_user_id);
END;
$$ LANGUAGE plpgsql;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_loyalty_tiers_updated_at BEFORE UPDATE ON loyalty_tiers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_loyalty_points_updated_at BEFORE UPDATE ON loyalty_points
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rewards_updated_at BEFORE UPDATE ON rewards
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reward_redemptions_updated_at BEFORE UPDATE ON reward_redemptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_loyalty_campaigns_updated_at BEFORE UPDATE ON loyalty_campaigns
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
