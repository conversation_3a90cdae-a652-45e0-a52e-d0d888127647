import { Request, Response } from 'express';
import { AuthenticatedRequest, ApiResponse, LoginResponse, RegisterResponse, CreateUserData } from '@/types';
import { UserModel } from '@/models/User';
import { SessionModel } from '@/models/Session';
import { RoleModel } from '@/models/Role';
import { JWTUtil } from '@/utils/jwt';
import { logger } from '@/utils/logger';
import { validationResult } from 'express-validator';
import crypto from 'crypto';

export class AuthController {
  /**
   * Register a new user
   */
  static async register(req: Request, res: Response): Promise<void> {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const response: ApiResponse = {
          success: false,
          message: 'Validation failed',
          errors: errors.array().map(err => err.msg),
        };
        res.status(400).json(response);
        return;
      }

      const { email, password, first_name, last_name, phone, date_of_birth, gender } = req.body;

      // Check if user already exists
      const existingUser = await UserModel.findByEmail(email);
      if (existingUser) {
        const response: ApiResponse = {
          success: false,
          message: 'User with this email already exists',
        };
        res.status(409).json(response);
        return;
      }

      // Create user data
      const userData: CreateUserData = {
        email,
        password,
        first_name,
        last_name,
        phone,
        date_of_birth: date_of_birth ? new Date(date_of_birth) : undefined,
        gender,
      };

      // Create user
      const user = await UserModel.create(userData);

      // Assign default customer role
      const customerRole = await RoleModel.findByName('customer');
      if (customerRole) {
        await RoleModel.assignRoleToUser(user.id, customerRole.id, user.id);
      }

      // Remove password from response
      const { password_hash, ...userResponse } = user;

      const response: RegisterResponse = {
        user: userResponse,
        message: 'User registered successfully. Please verify your email.',
      };

      logger.info('User registered successfully', { userId: user.id, email: user.email });
      res.status(201).json({ success: true, ...response });
    } catch (error) {
      logger.error('Registration error', { error });
      const response: ApiResponse = {
        success: false,
        message: 'Registration failed',
      };
      res.status(500).json(response);
    }
  }

  /**
   * Login user
   */
  static async login(req: Request, res: Response): Promise<void> {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const response: ApiResponse = {
          success: false,
          message: 'Validation failed',
          errors: errors.array().map(err => err.msg),
        };
        res.status(400).json(response);
        return;
      }

      const { email, password, remember_me = false } = req.body;

      // Find user
      const user = await UserModel.findByEmail(email);
      if (!user) {
        const response: ApiResponse = {
          success: false,
          message: 'Invalid email or password',
        };
        res.status(401).json(response);
        return;
      }

      // Check user status
      if (user.status !== 'active') {
        const response: ApiResponse = {
          success: false,
          message: 'Account is not active',
        };
        res.status(401).json(response);
        return;
      }

      // Verify password
      const isPasswordValid = await UserModel.verifyPassword(user, password);
      if (!isPasswordValid) {
        const response: ApiResponse = {
          success: false,
          message: 'Invalid email or password',
        };
        res.status(401).json(response);
        return;
      }

      // Get user roles
      const roles = await RoleModel.getUserRoles(user.id);
      const roleNames = roles.map(role => role.name);

      // Generate tokens
      const sessionId = crypto.randomUUID();
      const tokens = JWTUtil.generateTokenPair(user.id, user.email, roleNames, sessionId);

      // Create session
      const deviceInfo = req.headers['x-device-info'] as string;
      const ipAddress = req.ip;
      const userAgent = req.headers['user-agent'];
      
      const tokenHash = SessionModel.generateTokenHash(tokens.accessToken);
      const refreshTokenHash = SessionModel.generateTokenHash(tokens.refreshToken);
      
      const expiresAt = remember_me 
        ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
        : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);  // 7 days

      const session = await SessionModel.create(
        user.id,
        tokenHash,
        refreshTokenHash,
        deviceInfo,
        ipAddress,
        userAgent,
        expiresAt
      );

      // Update last login
      await UserModel.updateLastLogin(user.id);

      // Remove password from response
      const { password_hash, ...userResponse } = user;

      const response: LoginResponse = {
        user: userResponse,
        tokens: {
          accessToken: tokens.accessToken,
          refreshToken: tokens.refreshToken,
          expiresIn: tokens.expiresIn,
        },
        session: {
          id: session.id,
          expiresAt: session.expires_at,
        },
      };

      logger.info('User logged in successfully', { userId: user.id, email: user.email });
      res.json({ success: true, ...response });
    } catch (error) {
      logger.error('Login error', { error });
      const response: ApiResponse = {
        success: false,
        message: 'Login failed',
      };
      res.status(500).json(response);
    }
  }

  /**
   * Refresh access token
   */
  static async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const { refresh_token } = req.body;

      if (!refresh_token) {
        const response: ApiResponse = {
          success: false,
          message: 'Refresh token required',
        };
        res.status(400).json(response);
        return;
      }

      // Verify refresh token
      const payload = JWTUtil.verifyRefreshToken(refresh_token);
      
      // Find session
      const refreshTokenHash = SessionModel.generateTokenHash(refresh_token);
      const session = await SessionModel.findByRefreshTokenHash(refreshTokenHash);
      
      if (!session) {
        const response: ApiResponse = {
          success: false,
          message: 'Invalid refresh token',
        };
        res.status(401).json(response);
        return;
      }

      // Get user and roles
      const user = await UserModel.findById(payload.userId);
      if (!user || user.status !== 'active') {
        const response: ApiResponse = {
          success: false,
          message: 'User not found or inactive',
        };
        res.status(401).json(response);
        return;
      }

      const roles = await RoleModel.getUserRoles(user.id);
      const roleNames = roles.map(role => role.name);

      // Generate new tokens
      const tokens = JWTUtil.generateTokenPair(user.id, user.email, roleNames, session.id);

      // Update session with new tokens
      const newTokenHash = SessionModel.generateTokenHash(tokens.accessToken);
      const newRefreshTokenHash = SessionModel.generateTokenHash(tokens.refreshToken);
      
      await SessionModel.updateTokens(
        session.id,
        newTokenHash,
        newRefreshTokenHash,
        tokens.refreshTokenExpiration
      );

      const response: ApiResponse<{
        accessToken: string;
        refreshToken: string;
        expiresIn: number;
      }> = {
        success: true,
        message: 'Token refreshed successfully',
        data: {
          accessToken: tokens.accessToken,
          refreshToken: tokens.refreshToken,
          expiresIn: tokens.expiresIn,
        },
      };

      res.json(response);
    } catch (error) {
      logger.error('Token refresh error', { error });
      
      let message = 'Token refresh failed';
      if (error instanceof Error) {
        if (error.message.includes('expired')) {
          message = 'Refresh token expired';
        } else if (error.message.includes('invalid')) {
          message = 'Invalid refresh token';
        }
      }

      const response: ApiResponse = {
        success: false,
        message,
      };
      res.status(401).json(response);
    }
  }

  /**
   * Logout user
   */
  static async logout(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (req.session) {
        await SessionModel.deactivate(req.session.id);
      }

      const response: ApiResponse = {
        success: true,
        message: 'Logged out successfully',
      };

      logger.info('User logged out', { userId: req.user?.id });
      res.json(response);
    } catch (error) {
      logger.error('Logout error', { error });
      const response: ApiResponse = {
        success: false,
        message: 'Logout failed',
      };
      res.status(500).json(response);
    }
  }

  /**
   * Logout from all devices
   */
  static async logoutAll(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (req.user) {
        const deactivatedCount = await SessionModel.deactivateAllUserSessions(req.user.id);
        
        const response: ApiResponse = {
          success: true,
          message: `Logged out from ${deactivatedCount} devices`,
        };

        logger.info('User logged out from all devices', { 
          userId: req.user.id, 
          sessionsDeactivated: deactivatedCount 
        });
        res.json(response);
      } else {
        const response: ApiResponse = {
          success: false,
          message: 'User not authenticated',
        };
        res.status(401).json(response);
      }
    } catch (error) {
      logger.error('Logout all error', { error });
      const response: ApiResponse = {
        success: false,
        message: 'Logout all failed',
      };
      res.status(500).json(response);
    }
  }

  /**
   * Get current user profile
   */
  static async getProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        const response: ApiResponse = {
          success: false,
          message: 'User not authenticated',
        };
        res.status(401).json(response);
        return;
      }

      // Remove password from response
      const { password_hash, ...userResponse } = req.user;

      const response: ApiResponse = {
        success: true,
        message: 'Profile retrieved successfully',
        data: {
          user: userResponse,
          roles: req.roles?.map(role => ({
            id: role.id,
            name: role.name,
            description: role.description,
            permissions: role.permissions,
          })),
        },
      };

      res.json(response);
    } catch (error) {
      logger.error('Get profile error', { error });
      const response: ApiResponse = {
        success: false,
        message: 'Failed to retrieve profile',
      };
      res.status(500).json(response);
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        const response: ApiResponse = {
          success: false,
          message: 'User not authenticated',
        };
        res.status(401).json(response);
        return;
      }

      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const response: ApiResponse = {
          success: false,
          message: 'Validation failed',
          errors: errors.array().map(err => err.msg),
        };
        res.status(400).json(response);
        return;
      }

      const { first_name, last_name, phone, date_of_birth, gender } = req.body;

      const updateData = {
        first_name,
        last_name,
        phone,
        date_of_birth: date_of_birth ? new Date(date_of_birth) : undefined,
        gender,
      };

      // Remove undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key as keyof typeof updateData] === undefined) {
          delete updateData[key as keyof typeof updateData];
        }
      });

      const updatedUser = await UserModel.update(req.user.id, updateData);

      if (!updatedUser) {
        const response: ApiResponse = {
          success: false,
          message: 'User not found',
        };
        res.status(404).json(response);
        return;
      }

      // Remove password from response
      const { password_hash, ...userResponse } = updatedUser;

      const response: ApiResponse = {
        success: true,
        message: 'Profile updated successfully',
        data: { user: userResponse },
      };

      logger.info('User profile updated', { userId: req.user.id });
      res.json(response);
    } catch (error) {
      logger.error('Update profile error', { error });
      const response: ApiResponse = {
        success: false,
        message: 'Failed to update profile',
      };
      res.status(500).json(response);
    }
  }

  /**
   * Change password
   */
  static async changePassword(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        const response: ApiResponse = {
          success: false,
          message: 'User not authenticated',
        };
        res.status(401).json(response);
        return;
      }

      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const response: ApiResponse = {
          success: false,
          message: 'Validation failed',
          errors: errors.array().map(err => err.msg),
        };
        res.status(400).json(response);
        return;
      }

      const { current_password, new_password } = req.body;

      // Verify current password
      const isCurrentPasswordValid = await UserModel.verifyPassword(req.user, current_password);
      if (!isCurrentPasswordValid) {
        const response: ApiResponse = {
          success: false,
          message: 'Current password is incorrect',
        };
        res.status(400).json(response);
        return;
      }

      // Update password
      const success = await UserModel.updatePassword(req.user.id, new_password);

      if (!success) {
        const response: ApiResponse = {
          success: false,
          message: 'Failed to update password',
        };
        res.status(500).json(response);
        return;
      }

      // Deactivate all other sessions (optional security measure)
      await SessionModel.deactivateAllUserSessions(req.user.id, req.session?.id);

      const response: ApiResponse = {
        success: true,
        message: 'Password changed successfully',
      };

      logger.info('User password changed', { userId: req.user.id });
      res.json(response);
    } catch (error) {
      logger.error('Change password error', { error });
      const response: ApiResponse = {
        success: false,
        message: 'Failed to change password',
      };
      res.status(500).json(response);
    }
  }

  /**
   * Get user sessions
   */
  static async getSessions(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        const response: ApiResponse = {
          success: false,
          message: 'User not authenticated',
        };
        res.status(401).json(response);
        return;
      }

      const sessions = await SessionModel.getUserActiveSessions(req.user.id);

      // Remove sensitive data from sessions
      const safeSessions = sessions.map(session => ({
        id: session.id,
        device_info: session.device_info,
        ip_address: session.ip_address,
        user_agent: session.user_agent,
        last_activity_at: session.last_activity_at,
        created_at: session.created_at,
        expires_at: session.expires_at,
        is_current: session.id === req.session?.id,
      }));

      const response: ApiResponse = {
        success: true,
        message: 'Sessions retrieved successfully',
        data: { sessions: safeSessions },
      };

      res.json(response);
    } catch (error) {
      logger.error('Get sessions error', { error });
      const response: ApiResponse = {
        success: false,
        message: 'Failed to retrieve sessions',
      };
      res.status(500).json(response);
    }
  }

  /**
   * Revoke specific session
   */
  static async revokeSession(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        const response: ApiResponse = {
          success: false,
          message: 'User not authenticated',
        };
        res.status(401).json(response);
        return;
      }

      const { sessionId } = req.params;

      // Verify session belongs to user
      const session = await SessionModel.findById(sessionId);
      if (!session || session.user_id !== req.user.id) {
        const response: ApiResponse = {
          success: false,
          message: 'Session not found',
        };
        res.status(404).json(response);
        return;
      }

      // Deactivate session
      const success = await SessionModel.deactivate(sessionId);

      if (!success) {
        const response: ApiResponse = {
          success: false,
          message: 'Failed to revoke session',
        };
        res.status(500).json(response);
        return;
      }

      const response: ApiResponse = {
        success: true,
        message: 'Session revoked successfully',
      };

      logger.info('Session revoked', { userId: req.user.id, sessionId });
      res.json(response);
    } catch (error) {
      logger.error('Revoke session error', { error });
      const response: ApiResponse = {
        success: false,
        message: 'Failed to revoke session',
      };
      res.status(500).json(response);
    }
  }
}
