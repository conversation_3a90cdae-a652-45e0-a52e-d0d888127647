import { database } from '@/config/database';
import { User, CreateUserData, UpdateUserData, PaginationOptions, PaginatedResult } from '@/types';
import { logger } from '@/utils/logger';
import bcrypt from 'bcrypt';
import { config } from '@/config';

export class UserModel {
  /**
   * Create a new user
   */
  static async create(userData: CreateUserData): Promise<User> {
    try {
      const hashedPassword = await bcrypt.hash(userData.password, config.security.bcryptRounds);
      
      const query = `
        INSERT INTO users (
          email, password_hash, first_name, last_name, phone, 
          date_of_birth, gender, email_verified, status
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING *
      `;
      
      const values = [
        userData.email.toLowerCase(),
        hashedPassword,
        userData.first_name,
        userData.last_name,
        userData.phone || null,
        userData.date_of_birth || null,
        userData.gender || null,
        false, // email_verified
        'active' // status
      ];

      const result = await database.query(query, values);
      return result.rows[0];
    } catch (error) {
      logger.error('Error creating user', { userData: { ...userData, password: '[REDACTED]' }, error });
      throw error;
    }
  }

  /**
   * Find user by ID
   */
  static async findById(id: string): Promise<User | null> {
    try {
      const query = 'SELECT * FROM users WHERE id = $1 AND status != $2';
      const result = await database.query(query, [id, 'deleted']);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error finding user by ID', { id, error });
      throw error;
    }
  }

  /**
   * Find user by email
   */
  static async findByEmail(email: string): Promise<User | null> {
    try {
      const query = 'SELECT * FROM users WHERE email = $1 AND status != $2';
      const result = await database.query(query, [email.toLowerCase(), 'deleted']);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error finding user by email', { email, error });
      throw error;
    }
  }

  /**
   * Update user
   */
  static async update(id: string, updateData: UpdateUserData): Promise<User | null> {
    try {
      const fields = [];
      const values = [];
      let paramCount = 1;

      // Build dynamic update query
      Object.entries(updateData).forEach(([key, value]) => {
        if (value !== undefined) {
          fields.push(`${key} = $${paramCount}`);
          values.push(value);
          paramCount++;
        }
      });

      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      fields.push(`updated_at = $${paramCount}`);
      values.push(new Date());
      values.push(id);

      const query = `
        UPDATE users 
        SET ${fields.join(', ')}
        WHERE id = $${paramCount + 1} AND status != 'deleted'
        RETURNING *
      `;

      const result = await database.query(query, values);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error updating user', { id, updateData, error });
      throw error;
    }
  }

  /**
   * Update user password
   */
  static async updatePassword(id: string, newPassword: string): Promise<boolean> {
    try {
      const hashedPassword = await bcrypt.hash(newPassword, config.security.bcryptRounds);
      
      const query = `
        UPDATE users 
        SET password_hash = $1, updated_at = $2
        WHERE id = $3 AND status != 'deleted'
      `;
      
      const result = await database.query(query, [hashedPassword, new Date(), id]);
      return result.rowCount > 0;
    } catch (error) {
      logger.error('Error updating user password', { id, error });
      throw error;
    }
  }

  /**
   * Verify user password
   */
  static async verifyPassword(user: User, password: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, user.password_hash);
    } catch (error) {
      logger.error('Error verifying password', { userId: user.id, error });
      throw error;
    }
  }

  /**
   * Update user status
   */
  static async updateStatus(id: string, status: User['status']): Promise<boolean> {
    try {
      const query = `
        UPDATE users 
        SET status = $1, updated_at = $2
        WHERE id = $3
      `;
      
      const result = await database.query(query, [status, new Date(), id]);
      return result.rowCount > 0;
    } catch (error) {
      logger.error('Error updating user status', { id, status, error });
      throw error;
    }
  }

  /**
   * Verify user email
   */
  static async verifyEmail(id: string): Promise<boolean> {
    try {
      const query = `
        UPDATE users 
        SET email_verified = true, updated_at = $1
        WHERE id = $2 AND status != 'deleted'
      `;
      
      const result = await database.query(query, [new Date(), id]);
      return result.rowCount > 0;
    } catch (error) {
      logger.error('Error verifying user email', { id, error });
      throw error;
    }
  }

  /**
   * Update last login timestamp
   */
  static async updateLastLogin(id: string): Promise<boolean> {
    try {
      const query = `
        UPDATE users 
        SET last_login_at = $1, updated_at = $1
        WHERE id = $2 AND status != 'deleted'
      `;
      
      const result = await database.query(query, [new Date(), id]);
      return result.rowCount > 0;
    } catch (error) {
      logger.error('Error updating last login', { id, error });
      throw error;
    }
  }

  /**
   * Get paginated users
   */
  static async findAll(options: PaginationOptions): Promise<PaginatedResult<User>> {
    try {
      const { page, limit, sortBy = 'created_at', sortOrder = 'DESC' } = options;
      const offset = (page - 1) * limit;

      // Count total users
      const countQuery = 'SELECT COUNT(*) FROM users WHERE status != $1';
      const countResult = await database.query(countQuery, ['deleted']);
      const total = parseInt(countResult.rows[0].count);

      // Get users
      const query = `
        SELECT * FROM users 
        WHERE status != $1
        ORDER BY ${sortBy} ${sortOrder}
        LIMIT $2 OFFSET $3
      `;
      
      const result = await database.query(query, ['deleted', limit, offset]);
      
      const totalPages = Math.ceil(total / limit);
      
      return {
        data: result.rows,
        meta: {
          page,
          limit,
          total,
          totalPages,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
      };
    } catch (error) {
      logger.error('Error finding all users', { options, error });
      throw error;
    }
  }

  /**
   * Search users by email or name
   */
  static async search(searchTerm: string, options: PaginationOptions): Promise<PaginatedResult<User>> {
    try {
      const { page, limit, sortBy = 'created_at', sortOrder = 'DESC' } = options;
      const offset = (page - 1) * limit;
      const searchPattern = `%${searchTerm.toLowerCase()}%`;

      // Count total matching users
      const countQuery = `
        SELECT COUNT(*) FROM users 
        WHERE status != $1 AND (
          LOWER(email) LIKE $2 OR 
          LOWER(first_name) LIKE $2 OR 
          LOWER(last_name) LIKE $2 OR
          LOWER(CONCAT(first_name, ' ', last_name)) LIKE $2
        )
      `;
      const countResult = await database.query(countQuery, ['deleted', searchPattern]);
      const total = parseInt(countResult.rows[0].count);

      // Get matching users
      const query = `
        SELECT * FROM users 
        WHERE status != $1 AND (
          LOWER(email) LIKE $2 OR 
          LOWER(first_name) LIKE $2 OR 
          LOWER(last_name) LIKE $2 OR
          LOWER(CONCAT(first_name, ' ', last_name)) LIKE $2
        )
        ORDER BY ${sortBy} ${sortOrder}
        LIMIT $3 OFFSET $4
      `;
      
      const result = await database.query(query, ['deleted', searchPattern, limit, offset]);
      
      const totalPages = Math.ceil(total / limit);
      
      return {
        data: result.rows,
        meta: {
          page,
          limit,
          total,
          totalPages,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
      };
    } catch (error) {
      logger.error('Error searching users', { searchTerm, options, error });
      throw error;
    }
  }

  /**
   * Delete user (soft delete)
   */
  static async delete(id: string): Promise<boolean> {
    try {
      const query = `
        UPDATE users 
        SET status = 'deleted', updated_at = $1
        WHERE id = $2 AND status != 'deleted'
      `;
      
      const result = await database.query(query, [new Date(), id]);
      return result.rowCount > 0;
    } catch (error) {
      logger.error('Error deleting user', { id, error });
      throw error;
    }
  }

  /**
   * Check if email exists
   */
  static async emailExists(email: string, excludeUserId?: string): Promise<boolean> {
    try {
      let query = 'SELECT id FROM users WHERE email = $1 AND status != $2';
      const params = [email.toLowerCase(), 'deleted'];
      
      if (excludeUserId) {
        query += ' AND id != $3';
        params.push(excludeUserId);
      }
      
      const result = await database.query(query, params);
      return result.rows.length > 0;
    } catch (error) {
      logger.error('Error checking email existence', { email, excludeUserId, error });
      throw error;
    }
  }
}
