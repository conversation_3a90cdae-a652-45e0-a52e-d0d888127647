-- Marcat E-commerce Platform Database Setup Script
-- This script creates all databases and runs migrations
-- Run this script as a PostgreSQL superuser

-- =============================================================================
-- CREATE DATABASES
-- =============================================================================

-- Create databases for each microservice
CREATE DATABASE marcat_auth;
CREATE DATABASE marcat_stores;
CREATE DATABASE marcat_products;
CREATE DATABASE marcat_orders;
CREATE DATABASE marcat_payments;
CREATE DATABASE marcat_inventory;
CREATE DATABASE marcat_customers;
CREATE DATABASE marcat_reviews;
CREATE DATABASE marcat_loyalty;
CREATE DATABASE marcat_notifications;
CREATE DATABASE marcat_support;

-- =============================================================================
-- RUN MIGRATIONS
-- =============================================================================

-- Auth Database Migration
\c marcat_auth;
\i migrations/001_create_auth_tables.sql

-- Store Database Migration
\c marcat_stores;
\i migrations/002_create_store_tables.sql

-- Product Database Migration
\c marcat_products;
\i migrations/003_create_product_tables.sql

-- Order Database Migration
\c marcat_orders;
\i migrations/004_create_order_tables.sql

-- Payment Database Migration
\c marcat_payments;
\i migrations/005_create_payment_tables.sql

-- Inventory Database Migration
\c marcat_inventory;
\i migrations/006_create_inventory_tables.sql

-- Customer Database Migration
\c marcat_customers;
\i migrations/007_create_customer_tables.sql

-- Review Database Migration
\c marcat_reviews;
\i migrations/008_create_review_tables.sql

-- Loyalty Database Migration
\c marcat_loyalty;
\i migrations/009_create_loyalty_tables.sql

-- Notification Database Migration
\c marcat_notifications;
\i migrations/010_create_notification_tables.sql

-- Support Database Migration
\c marcat_support;
\i migrations/011_create_support_tables.sql

-- =============================================================================
-- RUN SEEDING (OPTIONAL - FOR DEVELOPMENT)
-- =============================================================================

-- Uncomment the following line to populate databases with sample data
-- \i seeds/001_seed_sample_data.sql

-- =============================================================================
-- SETUP COMPLETE
-- =============================================================================

\echo 'Database setup complete!'
\echo 'All databases have been created and migrations have been applied.'
\echo 'To populate with sample data, run: psql -f database/seeds/001_seed_sample_data.sql'
