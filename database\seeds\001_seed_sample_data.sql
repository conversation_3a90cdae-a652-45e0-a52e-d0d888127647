-- Database Seeding Script for Marcat E-commerce Platform
-- This script populates all databases with sample data for development and testing
-- Run this after all migration files have been executed

-- =============================================================================
-- AUTH DATABASE SEEDING
-- =============================================================================

-- Connect to auth database
\c marcat_auth;

-- Insert sample users
INSERT INTO users (id, email, password_hash, first_name, last_name, phone, email_verified, status) VALUES
('********-1111-1111-1111-********1111', '<EMAIL>', '$2b$12$LQv3c1yqBwEHFl5aBLEKnOEWGAihxICLc8hxK6F8mqauh9Uyy8/Eq', 'Admin', 'User', '+1234567890', TRUE, 'active'),
('22222222-2222-2222-2222-222222222222', '<EMAIL>', '$2b$12$LQv3c1yqBwEHFl5aBLEKnOEWGAihxICLc8hxK6F8mqauh9Uyy8/Eq', 'John', 'Doe', '+1234567891', TRUE, 'active'),
('33333333-3333-3333-3333-************', '<EMAIL>', '$2b$12$LQv3c1yqBwEHFl5aBLEKnOEWGAihxICLc8hxK6F8mqauh9Uyy8/Eq', 'Jane', 'Smith', '+1234567892', TRUE, 'active'),
('44444444-4444-4444-4444-444444444444', '<EMAIL>', '$2b$12$LQv3c1yqBwEHFl5aBLEKnOEWGAihxICLc8hxK6F8mqauh9Uyy8/Eq', 'Store', 'Owner', '+1234567893', TRUE, 'active'),
('********-5555-5555-5555-********5555', '<EMAIL>', '$2b$12$LQv3c1yqBwEHFl5aBLEKnOEWGAihxICLc8hxK6F8mqauh9Uyy8/Eq', 'Customer', 'Test', '+1234567894', TRUE, 'active');

-- Assign roles to users
INSERT INTO user_roles (user_id, role_id) VALUES
('********-1111-1111-1111-********1111', (SELECT id FROM roles WHERE name = 'admin')),
('22222222-2222-2222-2222-222222222222', (SELECT id FROM roles WHERE name = 'customer')),
('33333333-3333-3333-3333-************', (SELECT id FROM roles WHERE name = 'customer')),
('44444444-4444-4444-4444-444444444444', (SELECT id FROM roles WHERE name = 'store_owner')),
('********-5555-5555-5555-********5555', (SELECT id FROM roles WHERE name = 'customer'));

-- Insert sample addresses
INSERT INTO addresses (user_id, type, first_name, last_name, address_line_1, city, state, postal_code, country, is_default) VALUES
('22222222-2222-2222-2222-222222222222', 'both', 'John', 'Doe', '123 Main St', 'New York', 'NY', '10001', 'US', TRUE),
('33333333-3333-3333-3333-************', 'shipping', 'Jane', 'Smith', '456 Oak Ave', 'Los Angeles', 'CA', '90210', 'US', TRUE),
('44444444-4444-4444-4444-444444444444', 'both', 'Store', 'Owner', '789 Business Blvd', 'Chicago', 'IL', '60601', 'US', TRUE),
('********-5555-5555-5555-********5555', 'both', 'Customer', 'Test', '321 Test Lane', 'Miami', 'FL', '33101', 'US', TRUE);

-- =============================================================================
-- STORE DATABASE SEEDING
-- =============================================================================

-- Connect to store database
\c marcat_stores;

-- Insert sample stores
INSERT INTO stores (id, owner_id, name, slug, description, email, phone, status, commission_rate, verification_status) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '44444444-4444-4444-4444-444444444444', 'Fashion Forward', 'fashion-forward', 'Premium men''s fashion and accessories', '<EMAIL>', '+1555123456', 'active', 15.00, 'verified'),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '44444444-4444-4444-4444-444444444444', 'Urban Style Co', 'urban-style-co', 'Modern urban clothing for the contemporary man', '<EMAIL>', '+1555123457', 'active', 12.50, 'verified'),
('cccccccc-cccc-cccc-cccc-cccccccccccc', '44444444-4444-4444-4444-444444444444', 'Classic Menswear', 'classic-menswear', 'Timeless classic clothing and formal wear', '<EMAIL>', '+1555123458', 'active', 18.00, 'pending');

-- Insert store categories
INSERT INTO store_categories (name, slug, description, sort_order) VALUES
('Clothing', 'clothing', 'All types of men''s clothing', 1),
('Shoes', 'shoes', 'Footwear for men', 2),
('Accessories', 'accessories', 'Fashion accessories', 3),
('Formal Wear', 'formal-wear', 'Suits, ties, and formal clothing', 4),
('Casual Wear', 'casual-wear', 'Everyday casual clothing', 5);

-- Assign categories to stores
INSERT INTO store_category_assignments (store_id, category_id) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', (SELECT id FROM store_categories WHERE slug = 'clothing')),
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', (SELECT id FROM store_categories WHERE slug = 'accessories')),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', (SELECT id FROM store_categories WHERE slug = 'casual-wear')),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', (SELECT id FROM store_categories WHERE slug = 'shoes')),
('cccccccc-cccc-cccc-cccc-cccccccccccc', (SELECT id FROM store_categories WHERE slug = 'formal-wear'));

-- =============================================================================
-- PRODUCT DATABASE SEEDING
-- =============================================================================

-- Connect to product database
\c marcat_products;

-- Insert product categories
INSERT INTO categories (name, slug, description, sort_order) VALUES
('Shirts', 'shirts', 'All types of shirts', 1),
('Pants', 'pants', 'Trousers, jeans, and pants', 2),
('Jackets', 'jackets', 'Outerwear and jackets', 3),
('T-Shirts', 't-shirts', 'Casual t-shirts and tops', 4),
('Shoes', 'shoes', 'All types of footwear', 5),
('Accessories', 'accessories', 'Belts, watches, and accessories', 6);

-- Insert sample products
INSERT INTO products (id, store_id, name, slug, description, category_id, base_price, status, weight, dimensions) VALUES
('dddddddd-dddd-dddd-dddd-dddddddddddd', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Premium Cotton Dress Shirt', 'premium-cotton-dress-shirt', 'High-quality cotton dress shirt perfect for business and formal occasions', (SELECT id FROM categories WHERE slug = 'shirts'), 89.99, 'active', 0.3, '{"length": 30, "width": 20, "height": 2}'),
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Slim Fit Chinos', 'slim-fit-chinos', 'Comfortable slim-fit chino pants in various colors', (SELECT id FROM categories WHERE slug = 'pants'), 79.99, 'active', 0.5, '{"length": 40, "width": 15, "height": 3}'),
('ffffffff-ffff-ffff-ffff-ffffffffffff', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Urban Graphic Tee', 'urban-graphic-tee', 'Trendy graphic t-shirt with modern urban design', (SELECT id FROM categories WHERE slug = 't-shirts'), 29.99, 'active', 0.2, '{"length": 25, "width": 20, "height": 1}'),
('gggggggg-gggg-gggg-gggg-gggggggggggg', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Leather Sneakers', 'leather-sneakers', 'Premium leather sneakers for casual and semi-formal wear', (SELECT id FROM categories WHERE slug = 'shoes'), 149.99, 'active', 0.8, '{"length": 35, "width": 25, "height": 12}'),
('hhhhhhhh-hhhh-hhhh-hhhh-hhhhhhhhhhhh', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 'Classic Business Suit', 'classic-business-suit', 'Traditional two-piece business suit in navy blue', (SELECT id FROM categories WHERE slug = 'jackets'), 399.99, 'active', 1.2, '{"length": 60, "width": 40, "height": 5}');

-- Insert product variants
INSERT INTO product_variants (id, product_id, sku, price, attributes, stock_quantity, is_default) VALUES
-- Premium Cotton Dress Shirt variants
('********-1111-1111-1111-********1111', 'dddddddd-dddd-dddd-dddd-dddddddddddd', 'PCDS-WHITE-M', 89.99, '{"color": "White", "size": "M"}', 50, TRUE),
('********-1111-1111-1111-********1112', 'dddddddd-dddd-dddd-dddd-dddddddddddd', 'PCDS-WHITE-L', 89.99, '{"color": "White", "size": "L"}', 45, FALSE),
('********-1111-1111-1111-********1113', 'dddddddd-dddd-dddd-dddd-dddddddddddd', 'PCDS-BLUE-M', 89.99, '{"color": "Light Blue", "size": "M"}', 40, FALSE),
-- Slim Fit Chinos variants
('22222222-2222-2222-2222-222222222221', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'SFC-KHAKI-32', 79.99, '{"color": "Khaki", "size": "32"}', 35, TRUE),
('22222222-2222-2222-2222-222222222222', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'SFC-NAVY-32', 79.99, '{"color": "Navy", "size": "32"}', 30, FALSE),
-- Urban Graphic Tee variants
('33333333-3333-3333-3333-333333333331', 'ffffffff-ffff-ffff-ffff-ffffffffffff', 'UGT-BLACK-M', 29.99, '{"color": "Black", "size": "M"}', 100, TRUE),
('33333333-3333-3333-3333-333333333332', 'ffffffff-ffff-ffff-ffff-ffffffffffff', 'UGT-WHITE-M', 29.99, '{"color": "White", "size": "M"}', 80, FALSE),
-- Leather Sneakers variants
('44444444-4444-4444-4444-444444444441', 'gggggggg-gggg-gggg-gggg-gggggggggggg', 'LS-BROWN-10', 149.99, '{"color": "Brown", "size": "10"}', 25, TRUE),
('44444444-4444-4444-4444-444444444442', 'gggggggg-gggg-gggg-gggg-gggggggggggg', 'LS-BLACK-10', 149.99, '{"color": "Black", "size": "10"}', 20, FALSE),
-- Classic Business Suit variants
('********-5555-5555-5555-********5551', 'hhhhhhhh-hhhh-hhhh-hhhh-hhhhhhhhhhhh', 'CBS-NAVY-42R', 399.99, '{"color": "Navy", "size": "42R"}', 15, TRUE);

-- Insert product images
INSERT INTO product_images (product_id, variant_id, url, alt_text, position, is_primary) VALUES
('dddddddd-dddd-dddd-dddd-dddddddddddd', '********-1111-1111-1111-********1111', 'https://images.marcat.com/products/dress-shirt-white-1.jpg', 'Premium Cotton Dress Shirt - White', 1, TRUE),
('dddddddd-dddd-dddd-dddd-dddddddddddd', '********-1111-1111-1111-********1111', 'https://images.marcat.com/products/dress-shirt-white-2.jpg', 'Premium Cotton Dress Shirt - White Detail', 2, FALSE),
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '22222222-2222-2222-2222-222222222221', 'https://images.marcat.com/products/chinos-khaki-1.jpg', 'Slim Fit Chinos - Khaki', 1, TRUE),
('ffffffff-ffff-ffff-ffff-ffffffffffff', '33333333-3333-3333-3333-333333333331', 'https://images.marcat.com/products/graphic-tee-black-1.jpg', 'Urban Graphic Tee - Black', 1, TRUE),
('gggggggg-gggg-gggg-gggg-gggggggggggg', '44444444-4444-4444-4444-444444444441', 'https://images.marcat.com/products/sneakers-brown-1.jpg', 'Leather Sneakers - Brown', 1, TRUE),
('hhhhhhhh-hhhh-hhhh-hhhh-hhhhhhhhhhhh', '********-5555-5555-5555-********5551', 'https://images.marcat.com/products/suit-navy-1.jpg', 'Classic Business Suit - Navy', 1, TRUE);

-- =============================================================================
-- INVENTORY DATABASE SEEDING
-- =============================================================================

-- Connect to inventory database
\c marcat_inventory;

-- Insert inventory records for all variants
INSERT INTO inventory (variant_id, store_id, quantity_available, quantity_reserved, reorder_point, unit_cost) VALUES
('********-1111-1111-1111-********1111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 50, 0, 10, 45.00),
('********-1111-1111-1111-********1112', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 45, 0, 10, 45.00),
('********-1111-1111-1111-********1113', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 40, 0, 10, 45.00),
('22222222-2222-2222-2222-222222222221', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 35, 0, 8, 40.00),
('22222222-2222-2222-2222-222222222222', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 30, 0, 8, 40.00),
('33333333-3333-3333-3333-333333333331', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 100, 0, 20, 15.00),
('33333333-3333-3333-3333-333333333332', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 80, 0, 20, 15.00),
('44444444-4444-4444-4444-444444444441', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 25, 0, 5, 75.00),
('44444444-4444-4444-4444-444444444442', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 20, 0, 5, 75.00),
('********-5555-5555-5555-********5551', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 15, 0, 3, 200.00);

-- =============================================================================
-- CUSTOMER DATABASE SEEDING
-- =============================================================================

-- Connect to customer database
\c marcat_customers;

-- Insert customer preferences
INSERT INTO customer_preferences (user_id, preferred_currency, preferred_language, size_preferences) VALUES
('22222222-2222-2222-2222-222222222222', 'USD', 'en', '{"shirt": "M", "pants": "32", "shoes": "10"}'),
('33333333-3333-3333-3333-************', 'USD', 'en', '{"shirt": "S", "pants": "28", "shoes": "8"}'),
('********-5555-5555-5555-********5555', 'USD', 'en', '{"shirt": "L", "pants": "34", "shoes": "11"}');

-- Insert sample carts
INSERT INTO carts (id, user_id, subtotal, total_amount) VALUES
('cart1111-1111-1111-1111-********1111', '22222222-2222-2222-2222-222222222222', 119.98, 119.98),
('cart2222-2222-2222-2222-222222222222', '33333333-3333-3333-3333-************', 29.99, 29.99);

-- Insert cart items
INSERT INTO cart_items (cart_id, product_id, variant_id, store_id, quantity, unit_price, total_price, product_name, variant_sku, variant_attributes) VALUES
('cart1111-1111-1111-1111-********1111', 'dddddddd-dddd-dddd-dddd-dddddddddddd', '********-1111-1111-1111-********1111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 1, 89.99, 89.99, 'Premium Cotton Dress Shirt', 'PCDS-WHITE-M', '{"color": "White", "size": "M"}'),
('cart1111-1111-1111-1111-********1111', 'ffffffff-ffff-ffff-ffff-ffffffffffff', '33333333-3333-3333-3333-333333333331', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 1, 29.99, 29.99, 'Urban Graphic Tee', 'UGT-BLACK-M', '{"color": "Black", "size": "M"}'),
('cart2222-2222-2222-2222-222222222222', 'ffffffff-ffff-ffff-ffff-ffffffffffff', '33333333-3333-3333-3333-333333333332', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 1, 29.99, 29.99, 'Urban Graphic Tee', 'UGT-WHITE-M', '{"color": "White", "size": "M"}');

-- Insert wishlists
INSERT INTO wishlists (id, user_id, name, total_items) VALUES
('wish1111-1111-1111-1111-********1111', '22222222-2222-2222-2222-222222222222', 'My Wishlist', 2),
('wish2222-2222-2222-2222-222222222222', '33333333-3333-3333-3333-************', 'Favorites', 1);

-- Insert wishlist items
INSERT INTO wishlist_items (wishlist_id, product_id, store_id, product_name, product_price) VALUES
('wish1111-1111-1111-1111-********1111', 'gggggggg-gggg-gggg-gggg-gggggggggggg', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Leather Sneakers', 149.99),
('wish1111-1111-1111-1111-********1111', 'hhhhhhhh-hhhh-hhhh-hhhh-hhhhhhhhhhhh', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 'Classic Business Suit', 399.99),
('wish2222-2222-2222-2222-222222222222', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Slim Fit Chinos', 79.99);

-- =============================================================================
-- LOYALTY DATABASE SEEDING
-- =============================================================================

-- Connect to loyalty database
\c marcat_loyalty;

-- Insert loyalty points for users
INSERT INTO loyalty_points (user_id, total_points, available_points, current_tier_id, lifetime_points_earned) VALUES
('22222222-2222-2222-2222-222222222222', 1250, 1250, (SELECT id FROM loyalty_tiers WHERE slug = 'silver'), 1250),
('33333333-3333-3333-3333-************', 750, 750, (SELECT id FROM loyalty_tiers WHERE slug = 'bronze'), 750),
('********-5555-5555-5555-********5555', 2500, 2500, (SELECT id FROM loyalty_tiers WHERE slug = 'silver'), 2500);

-- Insert sample rewards
INSERT INTO rewards (name, description, reward_type, points_required, discount_percentage, is_active, is_featured) VALUES
('10% Off Next Purchase', 'Get 10% off your next order', 'discount', 500, 10.00, TRUE, TRUE),
('Free Shipping', 'Free shipping on your next order', 'free_shipping', 300, TRUE, TRUE),
('$25 Off', 'Get $25 off orders over $100', 'discount', 1000, 25.00, TRUE, FALSE),
('15% Off Premium Items', 'Special discount on premium products', 'discount', 1500, 15.00, TRUE, FALSE);

-- =============================================================================
-- NOTIFICATION DATABASE SEEDING
-- =============================================================================

-- Connect to notification database
\c marcat_notifications;

-- Insert notification templates
INSERT INTO notification_templates (name, slug, type, category, subject, title, body, is_active) VALUES
('Welcome Email', 'welcome-email', 'email', 'system', 'Welcome to Marcat!', 'Welcome to Marcat!', 'Thank you for joining Marcat. Start exploring our amazing collection of men''s fashion.', TRUE),
('Order Confirmation', 'order-confirmation', 'email', 'order', 'Order Confirmation - {{order_number}}', 'Order Confirmed', 'Your order {{order_number}} has been confirmed and is being processed.', TRUE),
('Shipping Notification', 'shipping-notification', 'email', 'order', 'Your Order is on the Way!', 'Order Shipped', 'Your order {{order_number}} has been shipped and is on its way to you.', TRUE),
('Low Stock Alert', 'low-stock-alert', 'in_app', 'system', NULL, 'Low Stock Alert', 'Product {{product_name}} is running low on stock.', TRUE);

-- Insert user notification preferences
INSERT INTO user_notification_preferences (user_id) VALUES
('22222222-2222-2222-2222-222222222222'),
('33333333-3333-3333-3333-************'),
('44444444-4444-4444-4444-444444444444'),
('********-5555-5555-5555-********5555');

-- Insert sample notifications
INSERT INTO notifications (user_id, title, message, type, category, status, is_read) VALUES
('22222222-2222-2222-2222-222222222222', 'Welcome to Marcat!', 'Thank you for joining our platform. Start exploring our amazing collection.', 'info', 'system', 'sent', FALSE),
('33333333-3333-3333-3333-************', 'New Products Available', 'Check out the latest arrivals in men''s fashion.', 'marketing', 'marketing', 'sent', FALSE),
('44444444-4444-4444-4444-444444444444', 'Store Verification Complete', 'Your store has been successfully verified and is now live.', 'success', 'system', 'sent', TRUE);

-- =============================================================================
-- SUPPORT DATABASE SEEDING
-- =============================================================================

-- Connect to support database
\c marcat_support;

-- Insert sample support tickets
INSERT INTO support_tickets (id, customer_id, created_by, subject, description, category_id, priority, status) VALUES
('ticket11-1111-1111-1111-********1111', '22222222-2222-2222-2222-222222222222', '22222222-2222-2222-2222-222222222222', 'Question about order status', 'I placed an order last week but haven''t received any updates. Can you help?', (SELECT id FROM support_categories WHERE slug = 'orders'), 'medium', 'open'),
('ticket22-2222-2222-2222-222222222222', '33333333-3333-3333-3333-************', '33333333-3333-3333-3333-************', 'Product size issue', 'The shirt I received doesn''t fit properly. I need to exchange it.', (SELECT id FROM support_categories WHERE slug = 'returns'), 'high', 'in_progress'),
('ticket33-3333-3333-3333-************', '********-5555-5555-5555-********5555', '********-5555-5555-5555-********5555', 'Payment not processed', 'My payment was declined but I was charged. Please help resolve this.', (SELECT id FROM support_categories WHERE slug = 'payments'), 'urgent', 'open');

-- Insert sample FAQ items
INSERT INTO faqs (question, answer, category_id, is_active, sort_order) VALUES
('How do I track my order?', 'You can track your order by logging into your account and visiting the "My Orders" section. You''ll find tracking information there once your order ships.', (SELECT id FROM support_categories WHERE slug = 'orders'), TRUE, 1),
('What is your return policy?', 'We offer a 30-day return policy for all items in original condition. Returns are free and easy - just use our online return portal.', (SELECT id FROM support_categories WHERE slug = 'returns'), TRUE, 2),
('Do you offer international shipping?', 'Yes, we ship to most countries worldwide. Shipping costs and delivery times vary by location.', (SELECT id FROM support_categories WHERE slug = 'shipping'), TRUE, 3),
('How do I change my password?', 'You can change your password by going to Account Settings > Security > Change Password.', (SELECT id FROM support_categories WHERE slug = 'account'), TRUE, 4);

-- Insert sample KB articles
INSERT INTO kb_articles (title, slug, content, category_id, author_id, status, published_at) VALUES
('Getting Started with Marcat', 'getting-started', 'Welcome to Marcat! This guide will help you get started with shopping on our platform...', (SELECT id FROM support_categories WHERE slug = 'general'), '********-1111-1111-1111-********1111', 'published', CURRENT_TIMESTAMP),
('Size Guide for Men''s Clothing', 'size-guide-mens-clothing', 'Finding the right size is important for a great fit. Here''s our comprehensive size guide...', (SELECT id FROM support_categories WHERE slug = 'products'), '********-1111-1111-1111-********1111', 'published', CURRENT_TIMESTAMP),
('How to Process Returns', 'how-to-process-returns', 'Step-by-step guide on how to return items you''re not satisfied with...', (SELECT id FROM support_categories WHERE slug = 'returns'), '********-1111-1111-1111-********1111', 'published', CURRENT_TIMESTAMP);

COMMIT;
