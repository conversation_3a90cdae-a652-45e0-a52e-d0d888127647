{"name": "marcat-platform", "version": "1.0.0", "description": "Multi-store e-commerce platform specializing in men's clothing", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:gateway\" \"npm run dev:auth\" \"npm run dev:product\" \"npm run dev:store\" \"npm run dev:order\" \"npm run dev:payment\"", "dev:gateway": "cd backend/api-gateway && npm run dev", "dev:auth": "cd backend/services/auth-service && npm run dev", "dev:product": "cd backend/services/product-service && npm run dev", "dev:store": "cd backend/services/store-service && npm run dev", "dev:order": "cd backend/services/order-service && npm run dev", "dev:payment": "cd backend/services/payment-service && npm run dev", "dev:inventory": "cd backend/services/inventory-service && npm run dev", "dev:notification": "cd backend/services/notification-service && npm run dev", "dev:review": "cd backend/services/review-service && npm run dev", "dev:loyalty": "cd backend/services/loyalty-service && npm run dev", "dev:recommendation": "cd backend/services/recommendation-service && npm run dev", "dev:support": "cd backend/services/support-service && npm run dev", "build": "npm run build:services && npm run build:gateway", "build:services": "concurrently \"npm run build:auth\" \"npm run build:product\" \"npm run build:store\" \"npm run build:order\" \"npm run build:payment\" \"npm run build:inventory\" \"npm run build:notification\" \"npm run build:review\" \"npm run build:loyalty\" \"npm run build:recommendation\" \"npm run build:support\"", "build:gateway": "cd backend/api-gateway && npm run build", "build:auth": "cd backend/services/auth-service && npm run build", "build:product": "cd backend/services/product-service && npm run build", "build:store": "cd backend/services/store-service && npm run build", "build:order": "cd backend/services/order-service && npm run build", "build:payment": "cd backend/services/payment-service && npm run build", "build:inventory": "cd backend/services/inventory-service && npm run build", "build:notification": "cd backend/services/notification-service && npm run build", "build:review": "cd backend/services/review-service && npm run build", "build:loyalty": "cd backend/services/loyalty-service && npm run build", "build:recommendation": "cd backend/services/recommendation-service && npm run build", "build:support": "cd backend/services/support-service && npm run build", "test": "npm run test:unit && npm run test:integration", "test:unit": "jest --config=tests/jest.config.js", "test:integration": "jest --config=tests/jest.integration.config.js", "test:e2e": "cypress run", "lint": "eslint backend/**/*.js frontend/**/*.dart", "lint:fix": "eslint backend/**/*.js --fix", "docker:build": "docker-compose -f deployment/docker/docker-compose.yml build", "docker:up": "docker-compose -f deployment/docker/docker-compose.yml up -d", "docker:down": "docker-compose -f deployment/docker/docker-compose.yml down", "k8s:deploy": "kubectl apply -f deployment/kubernetes/", "migrate:up": "cd database && npm run migrate:up", "migrate:down": "cd database && npm run migrate:down", "seed": "cd database && npm run seed"}, "keywords": ["ecommerce", "multi-store", "clothing", "marketplace", "microservices", "flutter"], "author": "Marcat Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "eslint": "^8.57.0", "jest": "^29.7.0", "cypress": "^13.6.4", "@types/node": "^20.11.5", "typescript": "^5.3.3", "nodemon": "^3.0.3"}, "dependencies": {"dotenv": "^16.4.1"}, "workspaces": ["backend/api-gateway", "backend/services/*", "backend/shared", "database"], "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/marcat/platform.git"}, "bugs": {"url": "https://github.com/marcat/platform/issues"}, "homepage": "https://github.com/marcat/platform#readme"}