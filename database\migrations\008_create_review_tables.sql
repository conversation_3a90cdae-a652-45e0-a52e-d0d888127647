-- Migration: Create Review Database Tables
-- Database: marcat_reviews
-- Created: 2025-07-03

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Reviews table
CREATE TABLE reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id from auth database
    product_id UUID, -- References products.id from product database
    store_id UUID, -- References stores.id from store database
    order_id UUID, -- References orders.id from order database
    
    -- Review Type
    review_type VARCHAR(20) NOT NULL CHECK (review_type IN ('product', 'store', 'order')),
    
    -- Review Content
    title VARCHAR(255),
    content TEXT NOT NULL,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    
    -- Review Attributes (for product reviews)
    attributes JSONB, -- {quality: 5, fit: 4, value: 5, etc.}
    
    -- Review Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'hidden')),
    
    -- Moderation
    moderated_by UUI<PERSON>, -- References users.id (admin who moderated)
    moderated_at TIMESTAMP,
    moderation_notes TEXT,
    
    -- Review Metrics
    helpful_count INTEGER DEFAULT 0,
    not_helpful_count INTEGER DEFAULT 0,
    
    -- Verification
    is_verified_purchase BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Review Images
CREATE TABLE review_images (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    review_id UUID REFERENCES reviews(id) ON DELETE CASCADE,
    
    -- Image Information
    url TEXT NOT NULL,
    alt_text VARCHAR(255),
    caption TEXT,
    
    -- Image Properties
    width INTEGER,
    height INTEGER,
    file_size INTEGER,
    mime_type VARCHAR(50),
    
    -- Image Organization
    position INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Review Helpfulness Votes
CREATE TABLE review_votes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    review_id UUID REFERENCES reviews(id) ON DELETE CASCADE,
    user_id UUID NOT NULL, -- References users.id
    
    -- Vote Details
    vote_type VARCHAR(20) NOT NULL CHECK (vote_type IN ('helpful', 'not_helpful')),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(review_id, user_id)
);

-- Review Responses (from store owners)
CREATE TABLE review_responses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    review_id UUID REFERENCES reviews(id) ON DELETE CASCADE,
    user_id UUID NOT NULL, -- References users.id (store owner/admin)
    
    -- Response Content
    content TEXT NOT NULL,
    
    -- Response Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'hidden', 'deleted')),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Review Reports (for inappropriate content)
CREATE TABLE review_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    review_id UUID REFERENCES reviews(id) ON DELETE CASCADE,
    reported_by UUID NOT NULL, -- References users.id
    
    -- Report Details
    reason VARCHAR(50) NOT NULL CHECK (reason IN ('spam', 'inappropriate', 'fake', 'offensive', 'other')),
    description TEXT,
    
    -- Report Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'resolved', 'dismissed')),
    
    -- Resolution
    resolved_by UUID, -- References users.id (admin)
    resolved_at TIMESTAMP,
    resolution_notes TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Review Templates (for common review scenarios)
CREATE TABLE review_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    store_id UUID, -- References stores.id (NULL for global templates)
    
    -- Template Details
    name VARCHAR(100) NOT NULL,
    title_template VARCHAR(255),
    content_template TEXT NOT NULL,
    
    -- Template Settings
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Review Reminders (to encourage reviews)
CREATE TABLE review_reminders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id
    order_id UUID NOT NULL, -- References orders.id
    product_id UUID, -- References products.id (for specific product)
    
    -- Reminder Details
    reminder_type VARCHAR(20) DEFAULT 'email' CHECK (reminder_type IN ('email', 'sms', 'push', 'in_app')),
    
    -- Reminder Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'clicked', 'reviewed', 'dismissed')),
    
    -- Scheduling
    scheduled_for TIMESTAMP NOT NULL,
    sent_at TIMESTAMP,
    clicked_at TIMESTAMP,
    
    -- Reminder Content
    subject VARCHAR(255),
    message TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Review Analytics (aggregated data)
CREATE TABLE review_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Dimensions
    date DATE NOT NULL,
    product_id UUID, -- References products.id (NULL for store-level analytics)
    store_id UUID, -- References stores.id
    
    -- Review Metrics
    total_reviews INTEGER DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    rating_distribution JSONB DEFAULT '{"1": 0, "2": 0, "3": 0, "4": 0, "5": 0}',
    
    -- Review Breakdown
    approved_reviews INTEGER DEFAULT 0,
    pending_reviews INTEGER DEFAULT 0,
    rejected_reviews INTEGER DEFAULT 0,
    
    -- Engagement Metrics
    total_helpful_votes INTEGER DEFAULT 0,
    total_responses INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(date, COALESCE(product_id, '00000000-0000-0000-0000-000000000000'::UUID), store_id)
);

-- Indexes for performance
CREATE INDEX idx_reviews_user_id ON reviews(user_id);
CREATE INDEX idx_reviews_product_id ON reviews(product_id);
CREATE INDEX idx_reviews_store_id ON reviews(store_id);
CREATE INDEX idx_reviews_order_id ON reviews(order_id);
CREATE INDEX idx_reviews_type ON reviews(review_type);
CREATE INDEX idx_reviews_status ON reviews(status);
CREATE INDEX idx_reviews_rating ON reviews(rating);
CREATE INDEX idx_reviews_created_at ON reviews(created_at DESC);
CREATE INDEX idx_reviews_verified ON reviews(is_verified_purchase);

CREATE INDEX idx_review_images_review_id ON review_images(review_id);

CREATE INDEX idx_review_votes_review_id ON review_votes(review_id);
CREATE INDEX idx_review_votes_user_id ON review_votes(user_id);
CREATE INDEX idx_review_votes_type ON review_votes(vote_type);

CREATE INDEX idx_review_responses_review_id ON review_responses(review_id);
CREATE INDEX idx_review_responses_user_id ON review_responses(user_id);

CREATE INDEX idx_review_reports_review_id ON review_reports(review_id);
CREATE INDEX idx_review_reports_status ON review_reports(status);

CREATE INDEX idx_review_reminders_user_id ON review_reminders(user_id);
CREATE INDEX idx_review_reminders_order_id ON review_reminders(order_id);
CREATE INDEX idx_review_reminders_status ON review_reminders(status);
CREATE INDEX idx_review_reminders_scheduled ON review_reminders(scheduled_for);

CREATE INDEX idx_review_analytics_date ON review_analytics(date DESC);
CREATE INDEX idx_review_analytics_product_id ON review_analytics(product_id);
CREATE INDEX idx_review_analytics_store_id ON review_analytics(store_id);

-- Function to update review helpfulness counts
CREATE OR REPLACE FUNCTION update_review_helpfulness(p_review_id UUID)
RETURNS VOID AS $$
DECLARE
    helpful_count INTEGER;
    not_helpful_count INTEGER;
BEGIN
    -- Count helpful votes
    SELECT COUNT(*) INTO helpful_count
    FROM review_votes
    WHERE review_id = p_review_id AND vote_type = 'helpful';
    
    -- Count not helpful votes
    SELECT COUNT(*) INTO not_helpful_count
    FROM review_votes
    WHERE review_id = p_review_id AND vote_type = 'not_helpful';
    
    -- Update review counts
    UPDATE reviews
    SET helpful_count = helpful_count,
        not_helpful_count = not_helpful_count,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_review_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update product/store ratings
CREATE OR REPLACE FUNCTION update_ratings(
    p_product_id UUID DEFAULT NULL,
    p_store_id UUID DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
    avg_rating DECIMAL(3,2);
    review_count INTEGER;
BEGIN
    IF p_product_id IS NOT NULL THEN
        -- Update product rating
        SELECT AVG(rating), COUNT(*)
        INTO avg_rating, review_count
        FROM reviews
        WHERE product_id = p_product_id 
          AND status = 'approved'
          AND review_type = 'product';
        
        -- Update would be done via API call to product service
        -- This is just for reference
        
    ELSIF p_store_id IS NOT NULL THEN
        -- Update store rating
        SELECT AVG(rating), COUNT(*)
        INTO avg_rating, review_count
        FROM reviews
        WHERE store_id = p_store_id 
          AND status = 'approved'
          AND review_type = 'store';
        
        -- Update would be done via API call to store service
        -- This is just for reference
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_reviews_updated_at BEFORE UPDATE ON reviews
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_review_responses_updated_at BEFORE UPDATE ON review_responses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_review_templates_updated_at BEFORE UPDATE ON review_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_review_analytics_updated_at BEFORE UPDATE ON review_analytics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger to update helpfulness counts when votes change
CREATE OR REPLACE FUNCTION trigger_update_review_helpfulness()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        PERFORM update_review_helpfulness(OLD.review_id);
        RETURN OLD;
    ELSE
        PERFORM update_review_helpfulness(NEW.review_id);
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_review_helpfulness_on_vote_change
    AFTER INSERT OR UPDATE OR DELETE ON review_votes
    FOR EACH ROW EXECUTE FUNCTION trigger_update_review_helpfulness();

-- Trigger to update ratings when reviews change
CREATE OR REPLACE FUNCTION trigger_update_ratings()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        PERFORM update_ratings(OLD.product_id, OLD.store_id);
        RETURN OLD;
    ELSE
        PERFORM update_ratings(NEW.product_id, NEW.store_id);
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_ratings_on_review_change
    AFTER INSERT OR UPDATE OR DELETE ON reviews
    FOR EACH ROW EXECUTE FUNCTION trigger_update_ratings();
