import { database } from '@/config/database';
import { Role, UserRole } from '@/types';
import { logger } from '@/utils/logger';

export class RoleModel {
  /**
   * Get all roles
   */
  static async findAll(): Promise<Role[]> {
    try {
      const query = 'SELECT * FROM roles WHERE is_active = true ORDER BY name';
      const result = await database.query(query);
      return result.rows;
    } catch (error) {
      logger.error('Error finding all roles', { error });
      throw error;
    }
  }

  /**
   * Find role by ID
   */
  static async findById(id: string): Promise<Role | null> {
    try {
      const query = 'SELECT * FROM roles WHERE id = $1 AND is_active = true';
      const result = await database.query(query, [id]);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error finding role by ID', { id, error });
      throw error;
    }
  }

  /**
   * Find role by name
   */
  static async findByName(name: string): Promise<Role | null> {
    try {
      const query = 'SELECT * FROM roles WHERE name = $1 AND is_active = true';
      const result = await database.query(query, [name]);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error finding role by name', { name, error });
      throw error;
    }
  }

  /**
   * Get user roles
   */
  static async getUserRoles(userId: string): Promise<Role[]> {
    try {
      const query = `
        SELECT r.* FROM roles r
        INNER JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = $1 AND r.is_active = true
        ORDER BY r.name
      `;
      const result = await database.query(query, [userId]);
      return result.rows;
    } catch (error) {
      logger.error('Error getting user roles', { userId, error });
      throw error;
    }
  }

  /**
   * Assign role to user
   */
  static async assignRoleToUser(userId: string, roleId: string, assignedBy: string): Promise<UserRole> {
    try {
      // Check if role assignment already exists
      const existingQuery = 'SELECT * FROM user_roles WHERE user_id = $1 AND role_id = $2';
      const existingResult = await database.query(existingQuery, [userId, roleId]);
      
      if (existingResult.rows.length > 0) {
        throw new Error('Role already assigned to user');
      }

      const query = `
        INSERT INTO user_roles (user_id, role_id, assigned_by)
        VALUES ($1, $2, $3)
        RETURNING *
      `;
      
      const result = await database.query(query, [userId, roleId, assignedBy]);
      return result.rows[0];
    } catch (error) {
      logger.error('Error assigning role to user', { userId, roleId, assignedBy, error });
      throw error;
    }
  }

  /**
   * Remove role from user
   */
  static async removeRoleFromUser(userId: string, roleId: string): Promise<boolean> {
    try {
      const query = 'DELETE FROM user_roles WHERE user_id = $1 AND role_id = $2';
      const result = await database.query(query, [userId, roleId]);
      return result.rowCount > 0;
    } catch (error) {
      logger.error('Error removing role from user', { userId, roleId, error });
      throw error;
    }
  }

  /**
   * Get users with specific role
   */
  static async getUsersWithRole(roleId: string): Promise<string[]> {
    try {
      const query = 'SELECT user_id FROM user_roles WHERE role_id = $1';
      const result = await database.query(query, [roleId]);
      return result.rows.map(row => row.user_id);
    } catch (error) {
      logger.error('Error getting users with role', { roleId, error });
      throw error;
    }
  }

  /**
   * Check if user has role
   */
  static async userHasRole(userId: string, roleName: string): Promise<boolean> {
    try {
      const query = `
        SELECT 1 FROM user_roles ur
        INNER JOIN roles r ON ur.role_id = r.id
        WHERE ur.user_id = $1 AND r.name = $2 AND r.is_active = true
      `;
      const result = await database.query(query, [userId, roleName]);
      return result.rows.length > 0;
    } catch (error) {
      logger.error('Error checking if user has role', { userId, roleName, error });
      throw error;
    }
  }

  /**
   * Check if user has permission
   */
  static async userHasPermission(userId: string, permission: string): Promise<boolean> {
    try {
      const query = `
        SELECT 1 FROM user_roles ur
        INNER JOIN roles r ON ur.role_id = r.id
        WHERE ur.user_id = $1 AND r.is_active = true AND $2 = ANY(r.permissions)
      `;
      const result = await database.query(query, [userId, permission]);
      return result.rows.length > 0;
    } catch (error) {
      logger.error('Error checking if user has permission', { userId, permission, error });
      throw error;
    }
  }

  /**
   * Get user permissions
   */
  static async getUserPermissions(userId: string): Promise<string[]> {
    try {
      const query = `
        SELECT DISTINCT unnest(r.permissions) as permission
        FROM user_roles ur
        INNER JOIN roles r ON ur.role_id = r.id
        WHERE ur.user_id = $1 AND r.is_active = true
        ORDER BY permission
      `;
      const result = await database.query(query, [userId]);
      return result.rows.map(row => row.permission);
    } catch (error) {
      logger.error('Error getting user permissions', { userId, error });
      throw error;
    }
  }

  /**
   * Create new role (admin only)
   */
  static async create(
    name: string,
    description: string,
    permissions: string[]
  ): Promise<Role> {
    try {
      const query = `
        INSERT INTO roles (name, description, permissions)
        VALUES ($1, $2, $3)
        RETURNING *
      `;
      
      const result = await database.query(query, [name, description, permissions]);
      return result.rows[0];
    } catch (error) {
      logger.error('Error creating role', { name, description, permissions, error });
      throw error;
    }
  }

  /**
   * Update role (admin only)
   */
  static async update(
    id: string,
    updates: Partial<Pick<Role, 'name' | 'description' | 'permissions' | 'is_active'>>
  ): Promise<Role | null> {
    try {
      const fields = [];
      const values = [];
      let paramCount = 1;

      Object.entries(updates).forEach(([key, value]) => {
        if (value !== undefined) {
          fields.push(`${key} = $${paramCount}`);
          values.push(value);
          paramCount++;
        }
      });

      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      fields.push(`updated_at = $${paramCount}`);
      values.push(new Date());
      values.push(id);

      const query = `
        UPDATE roles 
        SET ${fields.join(', ')}
        WHERE id = $${paramCount + 1}
        RETURNING *
      `;

      const result = await database.query(query, values);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error updating role', { id, updates, error });
      throw error;
    }
  }

  /**
   * Delete role (admin only)
   */
  static async delete(id: string): Promise<boolean> {
    try {
      // Check if role is assigned to any users
      const usersQuery = 'SELECT COUNT(*) FROM user_roles WHERE role_id = $1';
      const usersResult = await database.query(usersQuery, [id]);
      const userCount = parseInt(usersResult.rows[0].count);

      if (userCount > 0) {
        throw new Error(`Cannot delete role: assigned to ${userCount} users`);
      }

      const query = 'DELETE FROM roles WHERE id = $1';
      const result = await database.query(query, [id]);
      return result.rowCount > 0;
    } catch (error) {
      logger.error('Error deleting role', { id, error });
      throw error;
    }
  }

  /**
   * Deactivate role (soft delete)
   */
  static async deactivate(id: string): Promise<boolean> {
    try {
      const query = `
        UPDATE roles 
        SET is_active = false, updated_at = $1
        WHERE id = $2
      `;
      
      const result = await database.query(query, [new Date(), id]);
      return result.rowCount > 0;
    } catch (error) {
      logger.error('Error deactivating role', { id, error });
      throw error;
    }
  }

  /**
   * Get role statistics
   */
  static async getRoleStats(): Promise<{
    totalRoles: number;
    activeRoles: number;
    totalAssignments: number;
    roleAssignments: Array<{ roleName: string; userCount: number }>;
  }> {
    try {
      // Get total and active roles
      const rolesQuery = `
        SELECT 
          COUNT(*) as total_roles,
          COUNT(CASE WHEN is_active = true THEN 1 END) as active_roles
        FROM roles
      `;
      const rolesResult = await database.query(rolesQuery);
      
      // Get total assignments
      const assignmentsQuery = 'SELECT COUNT(*) as total_assignments FROM user_roles';
      const assignmentsResult = await database.query(assignmentsQuery);
      
      // Get role assignments breakdown
      const breakdownQuery = `
        SELECT r.name as role_name, COUNT(ur.user_id) as user_count
        FROM roles r
        LEFT JOIN user_roles ur ON r.id = ur.role_id
        WHERE r.is_active = true
        GROUP BY r.id, r.name
        ORDER BY user_count DESC, r.name
      `;
      const breakdownResult = await database.query(breakdownQuery);

      return {
        totalRoles: parseInt(rolesResult.rows[0].total_roles) || 0,
        activeRoles: parseInt(rolesResult.rows[0].active_roles) || 0,
        totalAssignments: parseInt(assignmentsResult.rows[0].total_assignments) || 0,
        roleAssignments: breakdownResult.rows.map(row => ({
          roleName: row.role_name,
          userCount: parseInt(row.user_count) || 0,
        })),
      };
    } catch (error) {
      logger.error('Error getting role statistics', { error });
      throw error;
    }
  }
}
