-- Migration: Create Notification Database Tables
-- Database: marcat_notifications
-- Created: 2025-07-03

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Notification Templates
CREATE TABLE notification_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Template Identification
    name VA<PERSON>HAR(100) UNIQUE NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- Template Type
    type VARCHAR(20) NOT NULL CHECK (type IN ('email', 'sms', 'push', 'in_app')),
    category VARCHAR(50) NOT NULL, -- 'order', 'marketing', 'system', 'loyalty', etc.
    
    -- Template Content
    subject VARCHAR(255), -- For email/push notifications
    title VARCHAR(255), -- For push/in-app notifications
    body TEXT NOT NULL,
    html_body TEXT, -- For email notifications
    
    -- Template Variables
    variables JSONB DEFAULT '[]', -- List of available variables
    
    -- Template Settings
    is_active BOOLEAN DEFAULT TRUE,
    is_system BOOLEAN DEFAULT FALSE, -- System templates cannot be deleted
    
    -- Localization
    language VARCHAR(5) DEFAULT 'en',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Notifications
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID, -- References users.id (NULL for system-wide notifications)
    
    -- Notification Content
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    
    -- Notification Type
    type VARCHAR(20) NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error', 'marketing')),
    category VARCHAR(50) NOT NULL,
    
    -- Notification Channels
    channels TEXT[] DEFAULT ARRAY['in_app'], -- 'email', 'sms', 'push', 'in_app'
    
    -- Notification Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'cancelled')),
    
    -- Read Status (for in-app notifications)
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP,
    
    -- Notification Priority
    priority VARCHAR(10) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    
    -- Scheduling
    scheduled_for TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sent_at TIMESTAMP,
    delivered_at TIMESTAMP,
    
    -- Reference Information
    reference_type VARCHAR(50), -- 'order', 'product', 'store', etc.
    reference_id UUID,
    
    -- Notification Data
    data JSONB DEFAULT '{}', -- Additional data for the notification
    
    -- Action Information
    action_url TEXT,
    action_text VARCHAR(50),
    
    -- Expiration
    expires_at TIMESTAMP,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Email Notifications
CREATE TABLE email_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
    
    -- Email Details
    to_email VARCHAR(255) NOT NULL,
    from_email VARCHAR(255) NOT NULL,
    reply_to VARCHAR(255),
    
    -- Email Content
    subject VARCHAR(255) NOT NULL,
    html_body TEXT,
    text_body TEXT,
    
    -- Email Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'bounced', 'failed', 'complained')),
    
    -- Provider Information
    provider VARCHAR(50), -- 'sendgrid', 'mailgun', 'ses', etc.
    provider_message_id VARCHAR(255),
    
    -- Delivery Information
    sent_at TIMESTAMP,
    delivered_at TIMESTAMP,
    opened_at TIMESTAMP,
    clicked_at TIMESTAMP,
    
    -- Error Information
    error_message TEXT,
    bounce_reason TEXT,
    
    -- Tracking
    open_count INTEGER DEFAULT 0,
    click_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- SMS Notifications
CREATE TABLE sms_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
    
    -- SMS Details
    to_phone VARCHAR(20) NOT NULL,
    from_phone VARCHAR(20) NOT NULL,
    
    -- SMS Content
    message TEXT NOT NULL,
    
    -- SMS Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'undelivered')),
    
    -- Provider Information
    provider VARCHAR(50), -- 'twilio', 'nexmo', etc.
    provider_message_id VARCHAR(255),
    
    -- Delivery Information
    sent_at TIMESTAMP,
    delivered_at TIMESTAMP,
    
    -- Error Information
    error_message TEXT,
    error_code VARCHAR(10),
    
    -- Cost Information
    cost DECIMAL(6,4), -- Cost in USD
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Push Notifications
CREATE TABLE push_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
    
    -- Push Details
    device_token VARCHAR(500) NOT NULL,
    platform VARCHAR(10) NOT NULL CHECK (platform IN ('ios', 'android', 'web')),
    
    -- Push Content
    title VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    icon_url TEXT,
    image_url TEXT,
    
    -- Push Settings
    badge_count INTEGER,
    sound VARCHAR(50) DEFAULT 'default',
    
    -- Push Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed')),
    
    -- Provider Information
    provider VARCHAR(50), -- 'fcm', 'apns', etc.
    provider_message_id VARCHAR(255),
    
    -- Delivery Information
    sent_at TIMESTAMP,
    delivered_at TIMESTAMP,
    clicked_at TIMESTAMP,
    
    -- Error Information
    error_message TEXT,
    
    -- Push Data
    custom_data JSONB DEFAULT '{}',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User Notification Preferences
CREATE TABLE user_notification_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID UNIQUE NOT NULL, -- References users.id
    
    -- Global Preferences
    email_enabled BOOLEAN DEFAULT TRUE,
    sms_enabled BOOLEAN DEFAULT FALSE,
    push_enabled BOOLEAN DEFAULT TRUE,
    in_app_enabled BOOLEAN DEFAULT TRUE,
    
    -- Category Preferences
    order_notifications JSONB DEFAULT '{"email": true, "sms": false, "push": true, "in_app": true}',
    marketing_notifications JSONB DEFAULT '{"email": true, "sms": false, "push": false, "in_app": false}',
    loyalty_notifications JSONB DEFAULT '{"email": true, "sms": false, "push": true, "in_app": true}',
    security_notifications JSONB DEFAULT '{"email": true, "sms": true, "push": true, "in_app": true}',
    system_notifications JSONB DEFAULT '{"email": true, "sms": false, "push": true, "in_app": true}',
    
    -- Frequency Preferences
    digest_frequency VARCHAR(20) DEFAULT 'weekly' CHECK (digest_frequency IN ('never', 'daily', 'weekly', 'monthly')),
    marketing_frequency VARCHAR(20) DEFAULT 'weekly' CHECK (marketing_frequency IN ('never', 'daily', 'weekly', 'monthly')),
    
    -- Quiet Hours
    quiet_hours_enabled BOOLEAN DEFAULT FALSE,
    quiet_hours_start TIME DEFAULT '22:00:00',
    quiet_hours_end TIME DEFAULT '08:00:00',
    quiet_hours_timezone VARCHAR(50) DEFAULT 'UTC',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Notification Campaigns (for bulk notifications)
CREATE TABLE notification_campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Campaign Details
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Campaign Type
    type VARCHAR(20) NOT NULL CHECK (type IN ('marketing', 'announcement', 'reminder', 'alert')),
    
    -- Campaign Content
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    
    -- Campaign Channels
    channels TEXT[] NOT NULL,
    
    -- Campaign Targeting
    target_audience JSONB, -- Criteria for targeting users
    estimated_recipients INTEGER DEFAULT 0,
    actual_recipients INTEGER DEFAULT 0,
    
    -- Campaign Status
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'sending', 'sent', 'cancelled')),
    
    -- Campaign Scheduling
    scheduled_for TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    
    -- Campaign Results
    sent_count INTEGER DEFAULT 0,
    delivered_count INTEGER DEFAULT 0,
    opened_count INTEGER DEFAULT 0,
    clicked_count INTEGER DEFAULT 0,
    failed_count INTEGER DEFAULT 0,
    
    -- Campaign Settings
    priority VARCHAR(10) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high')),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Notification Events (for tracking notification lifecycle)
CREATE TABLE notification_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
    
    -- Event Details
    event_type VARCHAR(20) NOT NULL CHECK (event_type IN ('sent', 'delivered', 'opened', 'clicked', 'bounced', 'failed')),
    channel VARCHAR(10) NOT NULL CHECK (channel IN ('email', 'sms', 'push', 'in_app')),
    
    -- Event Data
    event_data JSONB DEFAULT '{}',
    
    -- Event Metadata
    user_agent TEXT,
    ip_address INET,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_notification_templates_type ON notification_templates(type);
CREATE INDEX idx_notification_templates_category ON notification_templates(category);
CREATE INDEX idx_notification_templates_active ON notification_templates(is_active);

CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_category ON notifications(category);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_scheduled ON notifications(scheduled_for);
CREATE INDEX idx_notifications_reference ON notifications(reference_type, reference_id);
CREATE INDEX idx_notifications_unread ON notifications(user_id, is_read) WHERE is_read = FALSE;

CREATE INDEX idx_email_notifications_notification_id ON email_notifications(notification_id);
CREATE INDEX idx_email_notifications_status ON email_notifications(status);
CREATE INDEX idx_email_notifications_to_email ON email_notifications(to_email);

CREATE INDEX idx_sms_notifications_notification_id ON sms_notifications(notification_id);
CREATE INDEX idx_sms_notifications_status ON sms_notifications(status);
CREATE INDEX idx_sms_notifications_to_phone ON sms_notifications(to_phone);

CREATE INDEX idx_push_notifications_notification_id ON push_notifications(notification_id);
CREATE INDEX idx_push_notifications_status ON push_notifications(status);
CREATE INDEX idx_push_notifications_device_token ON push_notifications(device_token);

CREATE INDEX idx_user_notification_preferences_user_id ON user_notification_preferences(user_id);

CREATE INDEX idx_notification_campaigns_status ON notification_campaigns(status);
CREATE INDEX idx_notification_campaigns_scheduled ON notification_campaigns(scheduled_for);

CREATE INDEX idx_notification_events_notification_id ON notification_events(notification_id);
CREATE INDEX idx_notification_events_type ON notification_events(event_type);
CREATE INDEX idx_notification_events_channel ON notification_events(channel);

-- Function to check user notification preferences
CREATE OR REPLACE FUNCTION should_send_notification(
    p_user_id UUID,
    p_category VARCHAR,
    p_channel VARCHAR
)
RETURNS BOOLEAN AS $$
DECLARE
    preferences JSONB;
    channel_enabled BOOLEAN;
BEGIN
    -- Get user preferences for the category
    EXECUTE format('SELECT %I FROM user_notification_preferences WHERE user_id = $1', p_category || '_notifications')
    INTO preferences
    USING p_user_id;
    
    -- If no preferences found, use defaults
    IF preferences IS NULL THEN
        RETURN TRUE;
    END IF;
    
    -- Check if channel is enabled for this category
    channel_enabled := (preferences ->> p_channel)::BOOLEAN;
    
    RETURN COALESCE(channel_enabled, FALSE);
END;
$$ LANGUAGE plpgsql;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_notification_templates_updated_at BEFORE UPDATE ON notification_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notifications_updated_at BEFORE UPDATE ON notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_email_notifications_updated_at BEFORE UPDATE ON email_notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sms_notifications_updated_at BEFORE UPDATE ON sms_notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_push_notifications_updated_at BEFORE UPDATE ON push_notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_notification_preferences_updated_at BEFORE UPDATE ON user_notification_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_campaigns_updated_at BEFORE UPDATE ON notification_campaigns
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
