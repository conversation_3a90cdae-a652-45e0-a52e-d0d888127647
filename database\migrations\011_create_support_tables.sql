-- Migration: Create Support Database Tables
-- Database: marcat_support
-- Created: 2025-07-03

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Support Categories
CREATE TABLE support_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- Category Hierarchy
    parent_id UUID REFERENCES support_categories(id),
    
    -- Category Settings
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    
    -- Auto-assignment
    default_assignee_id UUID, -- References users.id (support agent)
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Support Tickets
CREATE TABLE support_tickets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ticket_number VARCHAR(20) UNIQUE NOT NULL,
    
    -- Ticket Details
    subject VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    
    -- Ticket Classification
    category_id UUID REFERENCES support_categories(id),
    priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    type VARCHAR(20) DEFAULT 'question' CHECK (type IN ('question', 'bug', 'feature_request', 'complaint', 'refund')),
    
    -- Ticket Status
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'waiting_customer', 'waiting_internal', 'resolved', 'closed')),
    
    -- Ticket Participants
    customer_id UUID NOT NULL, -- References users.id
    assigned_to UUID, -- References users.id (support agent)
    created_by UUID NOT NULL, -- References users.id
    
    -- Ticket Context
    order_id UUID, -- References orders.id if ticket is order-related
    product_id UUID, -- References products.id if ticket is product-related
    store_id UUID, -- References stores.id if ticket is store-related
    
    -- Ticket Metadata
    tags TEXT[] DEFAULT ARRAY[]::TEXT[],
    
    -- SLA Tracking
    first_response_at TIMESTAMP,
    resolved_at TIMESTAMP,
    closed_at TIMESTAMP,
    
    -- Customer Satisfaction
    satisfaction_rating INTEGER CHECK (satisfaction_rating >= 1 AND satisfaction_rating <= 5),
    satisfaction_feedback TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Ticket Messages
CREATE TABLE ticket_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ticket_id UUID REFERENCES support_tickets(id) ON DELETE CASCADE,
    
    -- Message Details
    message TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'reply' CHECK (message_type IN ('reply', 'note', 'system')),
    
    -- Message Author
    author_id UUID NOT NULL, -- References users.id
    author_type VARCHAR(20) NOT NULL CHECK (author_type IN ('customer', 'agent', 'system')),
    
    -- Message Visibility
    is_internal BOOLEAN DEFAULT FALSE, -- Internal notes not visible to customer
    is_system BOOLEAN DEFAULT FALSE, -- System-generated messages
    
    -- Message Status
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Ticket Attachments
CREATE TABLE ticket_attachments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ticket_id UUID REFERENCES support_tickets(id) ON DELETE CASCADE,
    message_id UUID REFERENCES ticket_messages(id) ON DELETE CASCADE,
    
    -- File Information
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    
    -- Upload Information
    uploaded_by UUID NOT NULL, -- References users.id
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Knowledge Base Articles
CREATE TABLE kb_articles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Article Details
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    
    -- Article Classification
    category_id UUID REFERENCES support_categories(id),
    tags TEXT[] DEFAULT ARRAY[]::TEXT[],
    
    -- Article Status
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    
    -- Article Metadata
    author_id UUID NOT NULL, -- References users.id
    featured BOOLEAN DEFAULT FALSE,
    
    -- SEO
    meta_title VARCHAR(255),
    meta_description TEXT,
    
    -- Article Stats
    view_count INTEGER DEFAULT 0,
    helpful_count INTEGER DEFAULT 0,
    not_helpful_count INTEGER DEFAULT 0,
    
    -- Publishing
    published_at TIMESTAMP,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Knowledge Base Article Votes
CREATE TABLE kb_article_votes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    article_id UUID REFERENCES kb_articles(id) ON DELETE CASCADE,
    user_id UUID, -- References users.id (NULL for anonymous votes)
    
    -- Vote Details
    vote_type VARCHAR(20) NOT NULL CHECK (vote_type IN ('helpful', 'not_helpful')),
    feedback TEXT,
    
    -- Vote Metadata
    ip_address INET,
    user_agent TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(article_id, COALESCE(user_id, '00000000-0000-0000-0000-000000000000'::UUID), ip_address)
);

-- FAQ Items
CREATE TABLE faqs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- FAQ Details
    question VARCHAR(500) NOT NULL,
    answer TEXT NOT NULL,
    
    -- FAQ Classification
    category_id UUID REFERENCES support_categories(id),
    tags TEXT[] DEFAULT ARRAY[]::TEXT[],
    
    -- FAQ Settings
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    
    -- FAQ Stats
    view_count INTEGER DEFAULT 0,
    helpful_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Live Chat Sessions
CREATE TABLE chat_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(100) UNIQUE NOT NULL,
    
    -- Session Participants
    customer_id UUID, -- References users.id (NULL for anonymous)
    agent_id UUID, -- References users.id
    
    -- Session Details
    status VARCHAR(20) DEFAULT 'waiting' CHECK (status IN ('waiting', 'active', 'ended', 'transferred')),
    
    -- Session Context
    page_url TEXT,
    referrer_url TEXT,
    user_agent TEXT,
    ip_address INET,
    
    -- Session Timing
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    agent_joined_at TIMESTAMP,
    ended_at TIMESTAMP,
    
    -- Session Metadata
    customer_name VARCHAR(100),
    customer_email VARCHAR(255),
    
    -- Session Rating
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    feedback TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Chat Messages
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES chat_sessions(id) ON DELETE CASCADE,
    
    -- Message Details
    message TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file', 'system')),
    
    -- Message Author
    sender_id UUID, -- References users.id (NULL for system messages)
    sender_type VARCHAR(20) NOT NULL CHECK (sender_type IN ('customer', 'agent', 'system')),
    
    -- Message Status
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP,
    
    -- File Information (for file messages)
    file_url TEXT,
    file_name VARCHAR(255),
    file_size INTEGER,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Support Agent Performance
CREATE TABLE agent_performance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID NOT NULL, -- References users.id
    
    -- Performance Period
    date DATE NOT NULL,
    
    -- Ticket Metrics
    tickets_assigned INTEGER DEFAULT 0,
    tickets_resolved INTEGER DEFAULT 0,
    tickets_closed INTEGER DEFAULT 0,
    
    -- Response Time Metrics
    avg_first_response_time INTERVAL,
    avg_resolution_time INTERVAL,
    
    -- Chat Metrics
    chat_sessions INTEGER DEFAULT 0,
    avg_chat_duration INTERVAL,
    
    -- Quality Metrics
    avg_satisfaction_rating DECIMAL(3,2),
    total_ratings INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(agent_id, date)
);

-- Indexes for performance
CREATE INDEX idx_support_categories_parent_id ON support_categories(parent_id);
CREATE INDEX idx_support_categories_active ON support_categories(is_active);

CREATE INDEX idx_support_tickets_number ON support_tickets(ticket_number);
CREATE INDEX idx_support_tickets_customer_id ON support_tickets(customer_id);
CREATE INDEX idx_support_tickets_assigned_to ON support_tickets(assigned_to);
CREATE INDEX idx_support_tickets_status ON support_tickets(status);
CREATE INDEX idx_support_tickets_priority ON support_tickets(priority);
CREATE INDEX idx_support_tickets_category_id ON support_tickets(category_id);
CREATE INDEX idx_support_tickets_created_at ON support_tickets(created_at DESC);

CREATE INDEX idx_ticket_messages_ticket_id ON ticket_messages(ticket_id);
CREATE INDEX idx_ticket_messages_author_id ON ticket_messages(author_id);
CREATE INDEX idx_ticket_messages_created_at ON ticket_messages(created_at);

CREATE INDEX idx_ticket_attachments_ticket_id ON ticket_attachments(ticket_id);
CREATE INDEX idx_ticket_attachments_message_id ON ticket_attachments(message_id);

CREATE INDEX idx_kb_articles_category_id ON kb_articles(category_id);
CREATE INDEX idx_kb_articles_status ON kb_articles(status);
CREATE INDEX idx_kb_articles_slug ON kb_articles(slug);
CREATE INDEX idx_kb_articles_featured ON kb_articles(featured);

CREATE INDEX idx_kb_article_votes_article_id ON kb_article_votes(article_id);

CREATE INDEX idx_faqs_category_id ON faqs(category_id);
CREATE INDEX idx_faqs_active ON faqs(is_active);

CREATE INDEX idx_chat_sessions_customer_id ON chat_sessions(customer_id);
CREATE INDEX idx_chat_sessions_agent_id ON chat_sessions(agent_id);
CREATE INDEX idx_chat_sessions_status ON chat_sessions(status);
CREATE INDEX idx_chat_sessions_started_at ON chat_sessions(started_at DESC);

CREATE INDEX idx_chat_messages_session_id ON chat_messages(session_id);
CREATE INDEX idx_chat_messages_sender_id ON chat_messages(sender_id);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);

CREATE INDEX idx_agent_performance_agent_id ON agent_performance(agent_id);
CREATE INDEX idx_agent_performance_date ON agent_performance(date DESC);

-- Function to generate ticket number
CREATE OR REPLACE FUNCTION generate_ticket_number()
RETURNS TEXT AS $$
DECLARE
    new_number TEXT;
    counter INTEGER;
BEGIN
    -- Get current date in format YYYYMMDD
    SELECT TO_CHAR(CURRENT_DATE, 'YYYYMMDD') INTO new_number;
    
    -- Get count of tickets created today
    SELECT COUNT(*) + 1 INTO counter
    FROM support_tickets 
    WHERE DATE(created_at) = CURRENT_DATE;
    
    -- Format: TICKET-YYYYMMDD-XXXX
    new_number := 'TICKET-' || new_number || '-' || LPAD(counter::TEXT, 4, '0');
    
    RETURN new_number;
END;
$$ LANGUAGE plpgsql;

-- Function to update KB article vote counts
CREATE OR REPLACE FUNCTION update_kb_article_votes(p_article_id UUID)
RETURNS VOID AS $$
DECLARE
    helpful_count INTEGER;
    not_helpful_count INTEGER;
BEGIN
    -- Count helpful votes
    SELECT COUNT(*) INTO helpful_count
    FROM kb_article_votes
    WHERE article_id = p_article_id AND vote_type = 'helpful';
    
    -- Count not helpful votes
    SELECT COUNT(*) INTO not_helpful_count
    FROM kb_article_votes
    WHERE article_id = p_article_id AND vote_type = 'not_helpful';
    
    -- Update article counts
    UPDATE kb_articles
    SET helpful_count = helpful_count,
        not_helpful_count = not_helpful_count,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_article_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_support_categories_updated_at BEFORE UPDATE ON support_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_support_tickets_updated_at BEFORE UPDATE ON support_tickets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_kb_articles_updated_at BEFORE UPDATE ON kb_articles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_faqs_updated_at BEFORE UPDATE ON faqs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_performance_updated_at BEFORE UPDATE ON agent_performance
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger to auto-generate ticket number
CREATE OR REPLACE FUNCTION set_ticket_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.ticket_number IS NULL THEN
        NEW.ticket_number := generate_ticket_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_ticket_number_trigger BEFORE INSERT ON support_tickets
    FOR EACH ROW EXECUTE FUNCTION set_ticket_number();

-- Trigger to update KB article vote counts
CREATE OR REPLACE FUNCTION trigger_update_kb_article_votes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        PERFORM update_kb_article_votes(OLD.article_id);
        RETURN OLD;
    ELSE
        PERFORM update_kb_article_votes(NEW.article_id);
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_kb_article_votes_on_vote_change
    AFTER INSERT OR UPDATE OR DELETE ON kb_article_votes
    FOR EACH ROW EXECUTE FUNCTION trigger_update_kb_article_votes();

-- Insert default support categories
INSERT INTO support_categories (name, slug, description, sort_order) VALUES
('General', 'general', 'General inquiries and questions', 1),
('Orders', 'orders', 'Order-related questions and issues', 2),
('Products', 'products', 'Product information and issues', 3),
('Payments', 'payments', 'Payment and billing questions', 4),
('Shipping', 'shipping', 'Shipping and delivery questions', 5),
('Returns', 'returns', 'Return and refund requests', 6),
('Technical', 'technical', 'Technical issues and bugs', 7),
('Account', 'account', 'Account and profile questions', 8);
