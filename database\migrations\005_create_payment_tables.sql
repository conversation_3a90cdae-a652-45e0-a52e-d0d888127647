-- Migration: Create Payment Database Tables
-- Database: marcat_payments
-- Created: 2025-07-03

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Payment Methods
CREATE TABLE payment_methods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- References users.id from auth database
    
    -- Payment Method Details
    type VARCHAR(20) NOT NULL CHECK (type IN ('credit_card', 'debit_card', 'paypal', 'bank_transfer', 'digital_wallet')),
    provider VARCHAR(50) NOT NULL, -- 'stripe', 'paypal', 'square', etc.
    provider_payment_method_id VARCHAR(255) NOT NULL,
    
    -- Card Information (for card payments)
    last_four VARCHAR(4),
    brand VARCHAR(20), -- 'visa', 'mastercard', 'amex', etc.
    exp_month INTEGER,
    exp_year INTEGER,
    
    -- Billing Information
    billing_address JSONB,
    
    -- Method Status
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payment Transactions
CREATE TABLE payment_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL, -- References orders.id from order database
    
    -- Transaction Identification
    transaction_id VARCHAR(255) UNIQUE NOT NULL,
    external_transaction_id VARCHAR(255), -- Provider's transaction ID
    
    -- Payment Information
    payment_method_id UUID REFERENCES payment_methods(id),
    payment_method_type VARCHAR(20) NOT NULL,
    gateway VARCHAR(50) NOT NULL, -- 'stripe', 'paypal', etc.
    
    -- Transaction Details
    type VARCHAR(20) DEFAULT 'payment' CHECK (type IN ('payment', 'refund', 'partial_refund', 'chargeback')),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    
    -- Transaction Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'succeeded', 'failed', 'cancelled', 'requires_action')),
    
    -- Gateway Response
    gateway_response JSONB,
    failure_reason TEXT,
    failure_code VARCHAR(50),
    
    -- Processing Information
    processed_at TIMESTAMP,
    gateway_fee DECIMAL(10,2),
    net_amount DECIMAL(10,2),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Refunds
CREATE TABLE refunds (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id UUID REFERENCES payment_transactions(id),
    order_id UUID NOT NULL, -- References orders.id
    return_request_id UUID, -- References return_requests.id if applicable
    
    -- Refund Identification
    refund_id VARCHAR(255) UNIQUE NOT NULL,
    external_refund_id VARCHAR(255), -- Provider's refund ID
    
    -- Refund Details
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    reason VARCHAR(100),
    description TEXT,
    
    -- Refund Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'succeeded', 'failed', 'cancelled')),
    
    -- Processing Information
    gateway_response JSONB,
    failure_reason TEXT,
    processed_at TIMESTAMP,
    
    -- Refund Breakdown
    refund_breakdown JSONB, -- {product_refunds: [], shipping_refund: 0, tax_refund: 0}
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payment Intents (for handling complex payment flows)
CREATE TABLE payment_intents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL, -- References orders.id
    
    -- Intent Identification
    intent_id VARCHAR(255) UNIQUE NOT NULL,
    external_intent_id VARCHAR(255), -- Provider's intent ID
    
    -- Intent Details
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    
    -- Intent Status
    status VARCHAR(30) DEFAULT 'requires_payment_method' CHECK (status IN (
        'requires_payment_method', 'requires_confirmation', 'requires_action', 
        'processing', 'requires_capture', 'cancelled', 'succeeded'
    )),
    
    -- Payment Method
    payment_method_types TEXT[] DEFAULT ARRAY['card'],
    
    -- Client Secret (for frontend)
    client_secret VARCHAR(500),
    
    -- Gateway Information
    gateway VARCHAR(50) NOT NULL,
    gateway_response JSONB,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payment Webhooks (for tracking webhook events)
CREATE TABLE payment_webhooks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Webhook Identification
    webhook_id VARCHAR(255) UNIQUE NOT NULL,
    external_webhook_id VARCHAR(255),
    
    -- Webhook Details
    event_type VARCHAR(100) NOT NULL,
    gateway VARCHAR(50) NOT NULL,
    
    -- Webhook Data
    payload JSONB NOT NULL,
    headers JSONB,
    
    -- Processing Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processed', 'failed', 'ignored')),
    processed_at TIMESTAMP,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    
    -- Related Records
    related_transaction_id UUID REFERENCES payment_transactions(id),
    related_refund_id UUID REFERENCES refunds(id),
    related_intent_id UUID REFERENCES payment_intents(id),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payment Disputes (chargebacks, disputes)
CREATE TABLE payment_disputes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id UUID REFERENCES payment_transactions(id),
    
    -- Dispute Identification
    dispute_id VARCHAR(255) UNIQUE NOT NULL,
    external_dispute_id VARCHAR(255),
    
    -- Dispute Details
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    reason VARCHAR(100),
    status VARCHAR(30) NOT NULL,
    
    -- Dispute Information
    evidence_due_by TIMESTAMP,
    is_charge_refundable BOOLEAN DEFAULT TRUE,
    
    -- Gateway Information
    gateway VARCHAR(50) NOT NULL,
    gateway_response JSONB,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payment Analytics (for reporting and insights)
CREATE TABLE payment_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Time Dimension
    date DATE NOT NULL,
    hour INTEGER CHECK (hour >= 0 AND hour <= 23),
    
    -- Dimensions
    gateway VARCHAR(50),
    payment_method_type VARCHAR(20),
    currency VARCHAR(3),
    
    -- Metrics
    transaction_count INTEGER DEFAULT 0,
    successful_count INTEGER DEFAULT 0,
    failed_count INTEGER DEFAULT 0,
    total_amount DECIMAL(12,2) DEFAULT 0.00,
    successful_amount DECIMAL(12,2) DEFAULT 0.00,
    refund_count INTEGER DEFAULT 0,
    refund_amount DECIMAL(12,2) DEFAULT 0.00,
    
    -- Calculated Fields
    success_rate DECIMAL(5,2) DEFAULT 0.00,
    average_transaction_amount DECIMAL(10,2) DEFAULT 0.00,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(date, hour, gateway, payment_method_type, currency)
);

-- Indexes for performance
CREATE INDEX idx_payment_methods_user_id ON payment_methods(user_id);
CREATE INDEX idx_payment_methods_type ON payment_methods(type);
CREATE INDEX idx_payment_methods_is_default ON payment_methods(is_default);

CREATE INDEX idx_payment_transactions_order_id ON payment_transactions(order_id);
CREATE INDEX idx_payment_transactions_transaction_id ON payment_transactions(transaction_id);
CREATE INDEX idx_payment_transactions_status ON payment_transactions(status);
CREATE INDEX idx_payment_transactions_gateway ON payment_transactions(gateway);
CREATE INDEX idx_payment_transactions_created_at ON payment_transactions(created_at DESC);

CREATE INDEX idx_refunds_transaction_id ON refunds(transaction_id);
CREATE INDEX idx_refunds_order_id ON refunds(order_id);
CREATE INDEX idx_refunds_status ON refunds(status);
CREATE INDEX idx_refunds_created_at ON refunds(created_at DESC);

CREATE INDEX idx_payment_intents_order_id ON payment_intents(order_id);
CREATE INDEX idx_payment_intents_intent_id ON payment_intents(intent_id);
CREATE INDEX idx_payment_intents_status ON payment_intents(status);

CREATE INDEX idx_payment_webhooks_webhook_id ON payment_webhooks(webhook_id);
CREATE INDEX idx_payment_webhooks_event_type ON payment_webhooks(event_type);
CREATE INDEX idx_payment_webhooks_status ON payment_webhooks(status);
CREATE INDEX idx_payment_webhooks_created_at ON payment_webhooks(created_at DESC);

CREATE INDEX idx_payment_disputes_transaction_id ON payment_disputes(transaction_id);
CREATE INDEX idx_payment_disputes_status ON payment_disputes(status);

CREATE INDEX idx_payment_analytics_date ON payment_analytics(date DESC);
CREATE INDEX idx_payment_analytics_gateway ON payment_analytics(gateway);

-- Function to generate transaction ID
CREATE OR REPLACE FUNCTION generate_transaction_id()
RETURNS TEXT AS $$
DECLARE
    new_id TEXT;
    counter INTEGER;
BEGIN
    -- Get current timestamp in format YYYYMMDDHHMISS
    SELECT TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS') INTO new_id;
    
    -- Get count of transactions created in current minute
    SELECT COUNT(*) + 1 INTO counter
    FROM payment_transactions 
    WHERE DATE_TRUNC('minute', created_at) = DATE_TRUNC('minute', CURRENT_TIMESTAMP);
    
    -- Format: TXN-YYYYMMDDHHMISS-XXX
    new_id := 'TXN-' || new_id || '-' || LPAD(counter::TEXT, 3, '0');
    
    RETURN new_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_payment_methods_updated_at BEFORE UPDATE ON payment_methods
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_transactions_updated_at BEFORE UPDATE ON payment_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_refunds_updated_at BEFORE UPDATE ON refunds
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_intents_updated_at BEFORE UPDATE ON payment_intents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_disputes_updated_at BEFORE UPDATE ON payment_disputes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_analytics_updated_at BEFORE UPDATE ON payment_analytics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger to auto-generate transaction ID
CREATE OR REPLACE FUNCTION set_transaction_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.transaction_id IS NULL THEN
        NEW.transaction_id := generate_transaction_id();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_transaction_id_trigger BEFORE INSERT ON payment_transactions
    FOR EACH ROW EXECUTE FUNCTION set_transaction_id();
