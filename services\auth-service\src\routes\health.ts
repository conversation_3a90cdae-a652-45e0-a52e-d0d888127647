import { Router, Request, Response } from 'express';
import { database } from '@/config/database';
import { redisClient } from '@/config/redis';
import { HealthCheckResult, ApiResponse } from '@/types';
import { config } from '@/config';
import { logger } from '@/utils/logger';

const router = Router();

/**
 * @route   GET /health
 * @desc    Basic health check
 * @access  Public
 */
router.get('/', async (req: Request, res: Response): Promise<void> => {
  try {
    const healthCheck: HealthCheckResult = {
      status: 'healthy',
      timestamp: new Date(),
      services: {
        database: false,
        redis: false,
      },
      uptime: process.uptime(),
      memory: {
        used: process.memoryUsage().heapUsed,
        total: process.memoryUsage().heapTotal,
        percentage: Math.round((process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100),
      },
    };

    // Check database connection
    try {
      await database.query('SELECT 1');
      healthCheck.services.database = true;
    } catch (error) {
      logger.error('Database health check failed', { error });
      healthCheck.status = 'unhealthy';
    }

    // Check Redis connection
    try {
      await redisClient.ping();
      healthCheck.services.redis = true;
    } catch (error) {
      logger.error('Redis health check failed', { error });
      healthCheck.status = 'unhealthy';
    }

    const statusCode = healthCheck.status === 'healthy' ? 200 : 503;
    
    const response: ApiResponse<HealthCheckResult> = {
      success: healthCheck.status === 'healthy',
      message: `Service is ${healthCheck.status}`,
      data: healthCheck,
    };

    res.status(statusCode).json(response);
  } catch (error) {
    logger.error('Health check error', { error });
    
    const response: ApiResponse = {
      success: false,
      message: 'Health check failed',
    };
    
    res.status(500).json(response);
  }
});

/**
 * @route   GET /health/ready
 * @desc    Readiness probe for Kubernetes
 * @access  Public
 */
router.get('/ready', async (req: Request, res: Response): Promise<void> => {
  try {
    // Check if all critical services are available
    await database.query('SELECT 1');
    await redisClient.ping();

    const response: ApiResponse = {
      success: true,
      message: 'Service is ready',
      data: {
        timestamp: new Date(),
        service: config.service.name,
        version: config.service.version,
      },
    };

    res.json(response);
  } catch (error) {
    logger.error('Readiness check failed', { error });
    
    const response: ApiResponse = {
      success: false,
      message: 'Service is not ready',
    };
    
    res.status(503).json(response);
  }
});

/**
 * @route   GET /health/live
 * @desc    Liveness probe for Kubernetes
 * @access  Public
 */
router.get('/live', (req: Request, res: Response): void => {
  const response: ApiResponse = {
    success: true,
    message: 'Service is alive',
    data: {
      timestamp: new Date(),
      uptime: process.uptime(),
      pid: process.pid,
    },
  };

  res.json(response);
});

/**
 * @route   GET /health/detailed
 * @desc    Detailed health check with service statistics
 * @access  Public
 */
router.get('/detailed', async (req: Request, res: Response): Promise<void> => {
  try {
    const healthCheck: any = {
      status: 'healthy',
      timestamp: new Date(),
      service: {
        name: config.service.name,
        version: config.service.version,
        environment: config.server.nodeEnv,
        uptime: process.uptime(),
        pid: process.pid,
      },
      system: {
        memory: {
          used: process.memoryUsage().heapUsed,
          total: process.memoryUsage().heapTotal,
          percentage: Math.round((process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100),
          rss: process.memoryUsage().rss,
          external: process.memoryUsage().external,
        },
        cpu: {
          usage: process.cpuUsage(),
        },
        node: {
          version: process.version,
          platform: process.platform,
          arch: process.arch,
        },
      },
      services: {
        database: {
          status: false,
          responseTime: 0,
          error: null,
        },
        redis: {
          status: false,
          responseTime: 0,
          error: null,
        },
      },
    };

    // Check database with timing
    try {
      const dbStart = Date.now();
      await database.query('SELECT 1');
      healthCheck.services.database.status = true;
      healthCheck.services.database.responseTime = Date.now() - dbStart;
    } catch (error) {
      healthCheck.status = 'unhealthy';
      healthCheck.services.database.error = error instanceof Error ? error.message : 'Unknown error';
      logger.error('Database detailed health check failed', { error });
    }

    // Check Redis with timing
    try {
      const redisStart = Date.now();
      await redisClient.ping();
      healthCheck.services.redis.status = true;
      healthCheck.services.redis.responseTime = Date.now() - redisStart;
    } catch (error) {
      healthCheck.status = 'unhealthy';
      healthCheck.services.redis.error = error instanceof Error ? error.message : 'Unknown error';
      logger.error('Redis detailed health check failed', { error });
    }

    // Get additional database statistics
    if (healthCheck.services.database.status) {
      try {
        const dbStats = await database.query(`
          SELECT 
            (SELECT COUNT(*) FROM users WHERE status = 'active') as active_users,
            (SELECT COUNT(*) FROM user_sessions WHERE is_active = true AND expires_at > NOW()) as active_sessions,
            (SELECT COUNT(*) FROM roles WHERE is_active = true) as active_roles
        `);
        
        healthCheck.statistics = {
          activeUsers: parseInt(dbStats.rows[0].active_users) || 0,
          activeSessions: parseInt(dbStats.rows[0].active_sessions) || 0,
          activeRoles: parseInt(dbStats.rows[0].active_roles) || 0,
        };
      } catch (error) {
        logger.warn('Failed to get database statistics', { error });
      }
    }

    const statusCode = healthCheck.status === 'healthy' ? 200 : 503;
    
    const response: ApiResponse = {
      success: healthCheck.status === 'healthy',
      message: `Service is ${healthCheck.status}`,
      data: healthCheck,
    };

    res.status(statusCode).json(response);
  } catch (error) {
    logger.error('Detailed health check error', { error });
    
    const response: ApiResponse = {
      success: false,
      message: 'Detailed health check failed',
    };
    
    res.status(500).json(response);
  }
});

/**
 * @route   GET /health/metrics
 * @desc    Service metrics for monitoring
 * @access  Public
 */
router.get('/metrics', async (req: Request, res: Response): Promise<void> => {
  try {
    const metrics = {
      timestamp: new Date(),
      service: config.service.name,
      version: config.service.version,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      environment: config.server.nodeEnv,
    };

    // Add database metrics if available
    try {
      const dbMetrics = await database.query(`
        SELECT 
          COUNT(*) as total_users,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
          COUNT(CASE WHEN created_at > NOW() - INTERVAL '24 hours' THEN 1 END) as users_last_24h
        FROM users
      `);
      
      const sessionMetrics = await database.query(`
        SELECT 
          COUNT(*) as total_sessions,
          COUNT(CASE WHEN is_active = true AND expires_at > NOW() THEN 1 END) as active_sessions,
          COUNT(CASE WHEN created_at > NOW() - INTERVAL '24 hours' THEN 1 END) as sessions_last_24h
        FROM user_sessions
      `);

      (metrics as any).database = {
        totalUsers: parseInt(dbMetrics.rows[0].total_users) || 0,
        activeUsers: parseInt(dbMetrics.rows[0].active_users) || 0,
        usersLast24h: parseInt(dbMetrics.rows[0].users_last_24h) || 0,
        totalSessions: parseInt(sessionMetrics.rows[0].total_sessions) || 0,
        activeSessions: parseInt(sessionMetrics.rows[0].active_sessions) || 0,
        sessionsLast24h: parseInt(sessionMetrics.rows[0].sessions_last_24h) || 0,
      };
    } catch (error) {
      logger.warn('Failed to get database metrics', { error });
    }

    const response: ApiResponse = {
      success: true,
      message: 'Metrics retrieved successfully',
      data: metrics,
    };

    res.json(response);
  } catch (error) {
    logger.error('Metrics endpoint error', { error });
    
    const response: ApiResponse = {
      success: false,
      message: 'Failed to retrieve metrics',
    };
    
    res.status(500).json(response);
  }
});

export default router;
